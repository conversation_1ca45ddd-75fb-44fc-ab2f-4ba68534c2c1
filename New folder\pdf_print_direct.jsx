/*
مولد PDF للطباعة المباشرة - Direct PDF Print Generator
إنشاء ملف PDF وفتحه مباشرة للطباعة أو في قارئ PDF خارجي
*/

// دالة إنشاء PDF للطباعة المباشرة
function createPrintablePDF(ui, price) {
    try {
        // إنشاء محتوى HTML منسق للطباعة
        var htmlContent = generatePrintableHTML(ui, price);
        
        // إنشاء ملف HTML مؤقت
        var tempFolder = Folder.temp;
        var htmlFile = new File(tempFolder + '/contract_' + new Date().getTime() + '.html');
        
        htmlFile.open('w');
        htmlFile.encoding = 'UTF-8';
        htmlFile.write(htmlContent);
        htmlFile.close();
        
        // فتح الملف في المتصفح للطباعة
        htmlFile.execute();
        
        alert('✅ تم إنشاء عقد التسعير بنجاح!\n\n' +
              '🖨️ سيتم فتح العقد في المتصفح\n\n' +
              '📋 يمكنك الآن:\n' +
              '• طباعة العقد مباشرة (Ctrl+P)\n' +
              '• حفظه كـ PDF من المتصفح\n' +
              '• نسخ المحتوى إذا احتجت\n\n' +
              '💡 العقد بخطوط كبيرة وتنسيق احترافي');
        
        return true;
        
    } catch (error) {
        alert('❌ خطأ في إنشاء PDF للطباعة: ' + error.message);
        return createAlternativePDF(ui, price);
    }
}

// دالة إنشاء محتوى HTML منسق
function generatePrintableHTML(ui, price) {
    var now = new Date();
    var contractNumber = 'DC-' + now.getFullYear() + '-' + 
                       (now.getMonth() + 1).toString().padStart(2, '0') + '-' + 
                       now.getDate().toString().padStart(2, '0') + '-' + 
                       Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    
    var designTypeIndex = ui.designTypeDropdown.selection.index;
    var designTypeKeys = Object.keys(PricingConfig.designTypes);
    var designType = PricingConfig.designTypes[designTypeKeys[designTypeIndex]];
    
    // حساب التفاصيل
    var basePrice = designType.basePrice * parseFloat(ui.timeInput.text);
    var complexityLevel = Math.round(ui.complexitySlider.value);
    var complexityKeys = Object.keys(PricingConfig.complexityMultipliers);
    var complexityData = PricingConfig.complexityMultipliers[complexityKeys[complexityLevel - 1]];
    
    var revisions = parseInt(ui.revisionsInput.text);
    var freeRevisions = 2;
    var paidRevisions = Math.max(0, revisions - freeRevisions);
    var revisionCost = 15;
    if (typeof PricingConfig !== 'undefined' && PricingConfig.additionalCosts && PricingConfig.additionalCosts.revisionCost) {
        revisionCost = PricingConfig.additionalCosts.revisionCost;
    }
    var revisionsCost = paidRevisions * revisionCost;
    
    // أنواع الاستخدام والتسليم
    var usageType = 'شخصي / Personal';
    if (ui.usageSmallBusiness && ui.usageSmallBusiness.value) usageType = 'مشروع صغير / Small Business';
    else if (ui.usageCommercial && ui.usageCommercial.value) usageType = 'تجاري / Commercial';
    else if (ui.usageEnterprise && ui.usageEnterprise.value) usageType = 'مؤسسي / Enterprise';
    
    var deliveryType = 'عادي / Normal';
    if (ui.deliveryFast && ui.deliveryFast.value) deliveryType = 'سريع / Fast';
    else if (ui.deliveryUrgent && ui.deliveryUrgent.value) deliveryType = 'مستعجل / Urgent';
    else if (ui.deliveryExpress && ui.deliveryExpress.value) deliveryType = 'فوري / Express';
    
    var html = '<!DOCTYPE html>\n';
    html += '<html dir="rtl" lang="ar">\n';
    html += '<head>\n';
    html += '<meta charset="UTF-8">\n';
    html += '<meta name="viewport" content="width=device-width, initial-scale=1.0">\n';
    html += '<title>عقد تسعير مشروع تصميم - ' + contractNumber + '</title>\n';
    html += '<style>\n';
    html += '@media print { @page { size: A4; margin: 1cm; } }\n';
    html += 'body { font-family: Arial, sans-serif; font-size: 16px; line-height: 1.6; margin: 0; padding: 20px; background: white; }\n';
    html += '.header { background: linear-gradient(135deg, #002f6c, #0066cc); color: white; padding: 30px; text-align: center; border-radius: 10px; margin-bottom: 30px; }\n';
    html += '.header h1 { font-size: 28px; margin: 0; font-weight: bold; }\n';
    html += '.header h2 { font-size: 18px; margin: 10px 0 0 0; font-weight: normal; }\n';
    html += '.section { border: 2px solid #0066cc; border-radius: 10px; padding: 20px; margin: 20px 0; background: #f8f9fa; }\n';
    html += '.section-title { font-size: 20px; font-weight: bold; color: #002f6c; margin-bottom: 15px; border-bottom: 2px solid #0066cc; padding-bottom: 10px; }\n';
    html += '.info-row { display: flex; justify-content: space-between; margin: 10px 0; padding: 8px; background: white; border-radius: 5px; }\n';
    html += '.info-label { font-weight: bold; color: #002f6c; }\n';
    html += '.info-value { color: #333; }\n';
    html += '.price-section { background: linear-gradient(135deg, #ff8c00, #ffa500); color: white; text-align: center; padding: 25px; border-radius: 10px; margin: 20px 0; }\n';
    html += '.price-section h2 { font-size: 24px; margin: 0; }\n';
    html += '.price-section .amount { font-size: 36px; font-weight: bold; margin: 10px 0; }\n';
    html += '.terms { background: #e8f4f8; border-left: 5px solid #0066cc; padding: 20px; margin: 20px 0; }\n';
    html += '.terms h3 { color: #002f6c; font-size: 18px; margin-bottom: 15px; }\n';
    html += '.terms ul { list-style-type: none; padding: 0; }\n';
    html += '.terms li { margin: 8px 0; padding: 5px 0; border-bottom: 1px dotted #ccc; }\n';
    html += '.footer { text-align: center; margin-top: 30px; padding: 20px; background: #f0f0f0; border-radius: 10px; }\n';
    html += '.print-btn { background: #0066cc; color: white; padding: 15px 30px; font-size: 18px; border: none; border-radius: 5px; cursor: pointer; margin: 10px; }\n';
    html += '.print-btn:hover { background: #004499; }\n';
    html += '@media print { .print-btn { display: none; } }\n';
    html += '</style>\n';
    html += '</head>\n';
    html += '<body>\n';
    
    // أزرار الطباعة والحفظ
    html += '<div style="text-align: center; margin-bottom: 20px;">\n';
    html += '<button class="print-btn" onclick="window.print()">🖨️ طباعة العقد</button>\n';
    html += '<button class="print-btn" onclick="savePDF()">💾 حفظ كـ PDF</button>\n';
    html += '</div>\n';
    
    // رأس العقد
    html += '<div class="header">\n';
    html += '<h1>عقد تسعير مشروع تصميم</h1>\n';
    html += '<h2>Design Project Pricing Contract</h2>\n';
    html += '</div>\n';
    
    // معلومات العقد
    html += '<div class="section">\n';
    html += '<div class="section-title">معلومات العقد - Contract Information</div>\n';
    html += '<div class="info-row"><span class="info-label">📋 رقم العقد / Contract Number:</span><span class="info-value">' + contractNumber + '</span></div>\n';
    html += '<div class="info-row"><span class="info-label">📅 التاريخ / Date:</span><span class="info-value">' + now.toLocaleDateString() + '</span></div>\n';
    html += '<div class="info-row"><span class="info-label">🕐 الوقت / Time:</span><span class="info-value">' + now.toLocaleTimeString() + '</span></div>\n';
    html += '</div>\n';
    
    // تفاصيل المشروع
    html += '<div class="section">\n';
    html += '<div class="section-title">تفاصيل المشروع - Project Details</div>\n';
    html += '<div class="info-row"><span class="info-label">🎨 نوع المشروع / Project Type:</span><span class="info-value">' + designType.name + '</span></div>\n';
    html += '<div class="info-row"><span class="info-label">⏰ عدد الساعات / Hours:</span><span class="info-value">' + ui.timeInput.text + ' ساعة / ' + ui.timeInput.text + ' Hours</span></div>\n';
    html += '<div class="info-row"><span class="info-label">🔧 درجة التعقيد / Complexity:</span><span class="info-value">' + ui.complexityValue.text + '</span></div>\n';
    html += '<div class="info-row"><span class="info-label">✏️ عدد التعديلات / Revisions:</span><span class="info-value">' + ui.revisionsInput.text + ' تعديل / ' + ui.revisionsInput.text + ' Revisions</span></div>\n';
    html += '<div class="info-row"><span class="info-label">👥 نوع الاستخدام / Usage Type:</span><span class="info-value">' + usageType + '</span></div>\n';
    html += '<div class="info-row"><span class="info-label">🚀 سرعة التسليم / Delivery Speed:</span><span class="info-value">' + deliveryType + '</span></div>\n';
    html += '</div>\n';
    
    // تفاصيل التسعير
    html += '<div class="section">\n';
    html += '<div class="section-title">تفاصيل التسعير - Pricing Breakdown</div>\n';
    html += '<div class="info-row"><span class="info-label">💵 السعر الأساسي / Base Price:</span><span class="info-value">' + ui.timeInput.text + ' ساعة × $' + designType.basePrice + ' = $' + Math.round(basePrice) + '</span></div>\n';
    html += '<div class="info-row"><span class="info-label">🔧 معامل التعقيد / Complexity Factor:</span><span class="info-value">' + complexityData.name + ' (×' + complexityData.multiplier + ')</span></div>\n';
    
    if (paidRevisions > 0) {
        html += '<div class="info-row"><span class="info-label">✏️ تعديلات إضافية / Additional Revisions:</span><span class="info-value">' + paidRevisions + ' × $' + revisionCost + ' = $' + revisionsCost + '</span></div>\n';
    } else {
        html += '<div class="info-row"><span class="info-label">✏️ التعديلات / Revisions:</span><span class="info-value">جميع التعديلات مشمولة مجاناً / All revisions included</span></div>\n';
    }
    html += '</div>\n';
    
    // السعر النهائي
    html += '<div class="price-section">\n';
    html += '<h2>إجمالي المبلغ المستحق / Total Amount Due</h2>\n';
    html += '<div class="amount">' + ui.currencySymbol.text + Math.round(price) + '</div>\n';
    html += '</div>\n';
    
    // الشروط والأحكام
    html += '<div class="terms">\n';
    html += '<h3>الشروط والأحكام - Terms & Conditions</h3>\n';
    html += '<ul>\n';
    html += '<li>📋 شروط الدفع: يتطلب دفع 50% مقدماً لبدء العمل، والمبلغ المتبقي عند التسليم النهائي</li>\n';
    html += '<li>⏰ مدة الصلاحية: هذا العرض صالح لمدة 30 يوماً من تاريخ الإصدار</li>\n';
    html += '<li>🔄 التعديلات: التعديلات الإضافية تتطلب موافقة مسبقة وقد تتطلب رسوم إضافية</li>\n';
    html += '<li>📅 التسليم: مدة التسليم تبدأ من تاريخ استلام المقدم والموافقة على المشروع</li>\n';
    html += '<li>⚖️ حقوق الملكية: حقوق الملكية تنتقل للعميل عند السداد الكامل للمبلغ المستحق</li>\n';
    html += '<li>📞 التواصل: للاستفسارات يرجى التواصل عبر البريد الإلكتروني أو الهاتف المذكور أدناه</li>\n';
    html += '</ul>\n';
    html += '</div>\n';
    
    // معلومات الاتصال
    html += '<div class="section">\n';
    html += '<div class="section-title">معلومات الاتصال - Contact Information</div>\n';
    html += '<div class="info-row"><span class="info-label">📧 البريد الإلكتروني / Email:</span><span class="info-value"><EMAIL></span></div>\n';
    html += '<div class="info-row"><span class="info-label">📱 الهاتف / Phone:</span><span class="info-value">+966 50 123 4567</span></div>\n';
    html += '<div class="info-row"><span class="info-label">🌐 الموقع الإلكتروني / Website:</span><span class="info-value">www.designstudio.com</span></div>\n';
    html += '<div class="info-row"><span class="info-label">📍 العنوان / Address:</span><span class="info-value">الرياض، المملكة العربية السعودية</span></div>\n';
    html += '</div>\n';
    
    // تذييل
    html += '<div class="footer">\n';
    html += '<p>تم إنشاؤه بواسطة حاسبة أسعار التصميم الاحترافية v2.0</p>\n';
    html += '<p>Generated by Design Price Calculator Pro v2.0</p>\n';
    html += '<p style="margin-top: 15px; font-weight: bold;">شكراً لثقتكم بنا - Thank you for your trust</p>\n';
    html += '</div>\n';
    
    // JavaScript للحفظ كـ PDF
    html += '<script>\n';
    html += 'function savePDF() {\n';
    html += '  if (window.print) {\n';
    html += '    alert("استخدم خيار الطباعة واختر \\"حفظ كـ PDF\\" من قائمة الطابعات");\n';
    html += '    window.print();\n';
    html += '  } else {\n';
    html += '    alert("استخدم Ctrl+P للطباعة أو حفظ كـ PDF");\n';
    html += '  }\n';
    html += '}\n';
    html += '</script>\n';
    
    html += '</body>\n';
    html += '</html>';
    
    return html;
}

// دالة بديلة في حالة فشل HTML
function createAlternativePDF(ui, price) {
    try {
        // إنشاء ملف نصي منسق للطباعة
        var file = File.saveDialog('حفظ عقد التسعير للطباعة - Save Contract for Printing', '*.txt');
        if (!file) return false;
        
        file.open('w');
        file.encoding = 'UTF-8';
        
        var content = generatePrintableText(ui, price);
        file.write(content);
        file.close();
        
        // محاولة فتح الملف
        try {
            file.execute();
        } catch (e) {
            // في حالة فشل الفتح التلقائي
        }
        
        alert('✅ تم إنشاء عقد التسعير بنجاح!\n\n' +
              '📄 الملف محفوظ في:\n' + file.fsName + '\n\n' +
              '🖨️ يمكنك الآن:\n' +
              '• فتح الملف وطباعته\n' +
              '• نسخه إلى Word وحفظه كـ PDF\n' +
              '• استخدام أي برنامج لتحويله إلى PDF');
        
        return true;
        
    } catch (error) {
        alert('❌ خطأ في إنشاء الملف البديل: ' + error.message);
        return false;
    }
}

// دالة إنشاء نص منسق للطباعة
function generatePrintableText(ui, price) {
    // نفس محتوى العقد النصي من contract_generator.jsx
    var content = '';
    var now = new Date();
    var contractNumber = 'DC-' + now.getFullYear() + '-' + 
                       (now.getMonth() + 1).toString().padStart(2, '0') + '-' + 
                       now.getDate().toString().padStart(2, '0') + '-' + 
                       Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    
    content += '████████████████████████████████████████████████████████████████████████████████\n';
    content += '█                          عقد تسعير مشروع تصميم                          █\n';
    content += '█                      DESIGN PROJECT PRICING CONTRACT                        █\n';
    content += '████████████████████████████████████████████████████████████████████████████████\n\n';
    
    content += 'رقم العقد / Contract Number: ' + contractNumber + '\n';
    content += 'التاريخ / Date: ' + now.toLocaleDateString() + '\n';
    content += 'الوقت / Time: ' + now.toLocaleTimeString() + '\n\n';
    
    content += '═══════════════════════════════════════════════════════════════════════════════\n';
    content += '                          إجمالي المبلغ المستحق\n';
    content += '                            TOTAL AMOUNT DUE\n';
    content += '                              ' + ui.currencySymbol.text + Math.round(price) + '\n';
    content += '═══════════════════════════════════════════════════════════════════════════════\n\n';
    
    content += 'للطباعة: اضغط Ctrl+P أو استخدم قائمة الطباعة في برنامجك المفضل\n';
    content += 'لحفظ كـ PDF: استخدم خيار "طباعة إلى PDF" أو "Microsoft Print to PDF"\n\n';
    
    return content;
}

// الدالة الرئيسية للتصدير
function exportToPDF(ui, price) {
    try {
        var choice = confirm('📄 اختر طريقة إنشاء عقد التسعير:\n\n' +
                           '✅ موافق = فتح في المتصفح للطباعة المباشرة\n' +
                           '❌ إلغاء = حفظ كملف نصي منسق\n\n' +
                           '💡 الخيار الأول يفتح عقد HTML جاهز للطباعة أو الحفظ كـ PDF');
        
        if (choice) {
            return createPrintablePDF(ui, price);
        } else {
            return createAlternativePDF(ui, price);
        }
        
    } catch (error) {
        alert('❌ خطأ في تصدير العقد: ' + error.message);
        return createAlternativePDF(ui, price);
    }
}
