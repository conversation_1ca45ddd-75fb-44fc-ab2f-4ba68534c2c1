/*
مولد العقود الاحترافي - Professional Contract Generator
إنشاء عقود تسعير منسقة بخطوط كبيرة وتنسيق احترافي
*/

// دالة إنشاء عقد تسعير احترافي
function createProfessionalContract(ui, price) {
    try {
        var file = File.saveDialog('حفظ عقد التسعير الاحترافي - Save Professional Contract', '*.txt');
        if (!file) return false;
        
        file.open('w');
        file.encoding = 'UTF-8';
        
        var now = new Date();
        var contractNumber = 'DC-' + now.getFullYear() + '-' + 
                           (now.getMonth() + 1).toString().padStart(2, '0') + '-' + 
                           now.getDate().toString().padStart(2, '0') + '-' + 
                           Math.floor(Math.random() * 1000).toString().padStart(3, '0');
        
        // رأس العقد الاحترافي
        file.writeln('');
        file.writeln('████████████████████████████████████████████████████████████████████████████████');
        file.writeln('█                                                                              █');
        file.writeln('█                          عقد تسعير مشروع تصميم                          █');
        file.writeln('█                      DESIGN PROJECT PRICING CONTRACT                        █');
        file.writeln('█                                                                              █');
        file.writeln('████████████████████████████████████████████████████████████████████████████████');
        file.writeln('');
        file.writeln('');
        
        // معلومات العقد
        file.writeln('┌─────────────────────────────────────────────────────────────────────────────┐');
        file.writeln('│                              معلومات العقد                                │');
        file.writeln('│                            CONTRACT INFORMATION                             │');
        file.writeln('├─────────────────────────────────────────────────────────────────────────────┤');
        file.writeln('│                                                                             │');
        file.writeln('│  📋 رقم العقد / Contract Number: ' + contractNumber.padEnd(35) + '│');
        file.writeln('│  📅 التاريخ / Date: ' + now.toLocaleDateString().padEnd(47) + '│');
        file.writeln('│  🕐 الوقت / Time: ' + now.toLocaleTimeString().padEnd(49) + '│');
        file.writeln('│                                                                             │');
        file.writeln('└─────────────────────────────────────────────────────────────────────────────┘');
        file.writeln('');
        file.writeln('');
        
        // تفاصيل المشروع
        file.writeln('┌─────────────────────────────────────────────────────────────────────────────┐');
        file.writeln('│                              تفاصيل المشروع                               │');
        file.writeln('│                             PROJECT DETAILS                                │');
        file.writeln('├─────────────────────────────────────────────────────────────────────────────┤');
        file.writeln('│                                                                             │');
        
        var designTypeIndex = ui.designTypeDropdown.selection.index;
        var designTypeKeys = Object.keys(PricingConfig.designTypes);
        var designType = PricingConfig.designTypes[designTypeKeys[designTypeIndex]];
        
        file.writeln('│  🎨 نوع المشروع / Project Type:                                           │');
        file.writeln('│      ' + designType.name.padEnd(65) + '│');
        file.writeln('│                                                                             │');
        file.writeln('│  ⏰ عدد الساعات المطلوبة / Required Hours:                               │');
        file.writeln('│      ' + (ui.timeInput.text + ' ساعة / ' + ui.timeInput.text + ' Hours').padEnd(65) + '│');
        file.writeln('│                                                                             │');
        file.writeln('│  🔧 درجة التعقيد / Complexity Level:                                      │');
        file.writeln('│      ' + ui.complexityValue.text.padEnd(65) + '│');
        file.writeln('│                                                                             │');
        file.writeln('│  ✏️ عدد التعديلات المطلوبة / Required Revisions:                         │');
        file.writeln('│      ' + (ui.revisionsInput.text + ' تعديل / ' + ui.revisionsInput.text + ' Revisions').padEnd(65) + '│');
        file.writeln('│                                                                             │');
        
        // نوع الاستخدام
        var usageType = 'شخصي / Personal';
        if (ui.usageSmallBusiness && ui.usageSmallBusiness.value) usageType = 'مشروع صغير / Small Business';
        else if (ui.usageCommercial && ui.usageCommercial.value) usageType = 'تجاري / Commercial';
        else if (ui.usageEnterprise && ui.usageEnterprise.value) usageType = 'مؤسسي / Enterprise';
        
        file.writeln('│  👥 نوع الاستخدام / Usage Type:                                           │');
        file.writeln('│      ' + usageType.padEnd(65) + '│');
        file.writeln('│                                                                             │');
        
        // سرعة التسليم
        var deliveryType = 'عادي / Normal';
        if (ui.deliveryFast && ui.deliveryFast.value) deliveryType = 'سريع / Fast';
        else if (ui.deliveryUrgent && ui.deliveryUrgent.value) deliveryType = 'مستعجل / Urgent';
        else if (ui.deliveryExpress && ui.deliveryExpress.value) deliveryType = 'فوري / Express';
        
        file.writeln('│  🚀 سرعة التسليم / Delivery Speed:                                        │');
        file.writeln('│      ' + deliveryType.padEnd(65) + '│');
        file.writeln('│                                                                             │');
        file.writeln('└─────────────────────────────────────────────────────────────────────────────┘');
        file.writeln('');
        file.writeln('');
        
        // تفاصيل التسعير
        file.writeln('┌─────────────────────────────────────────────────────────────────────────────┐');
        file.writeln('│                              تفاصيل التسعير                               │');
        file.writeln('│                             PRICING BREAKDOWN                               │');
        file.writeln('├─────────────────────────────────────────────────────────────────────────────┤');
        file.writeln('│                                                                             │');
        
        // السعر الأساسي
        var basePrice = designType.basePrice * parseFloat(ui.timeInput.text);
        file.writeln('│  💵 السعر الأساسي / Base Price:                                           │');
        file.writeln('│      ' + (ui.timeInput.text + ' ساعة × $' + designType.basePrice + ' = $' + Math.round(basePrice)).padEnd(65) + '│');
        file.writeln('│                                                                             │');
        
        // معامل التعقيد
        var complexityLevel = Math.round(ui.complexitySlider.value);
        var complexityKeys = Object.keys(PricingConfig.complexityMultipliers);
        var complexityData = PricingConfig.complexityMultipliers[complexityKeys[complexityLevel - 1]];
        
        file.writeln('│  🔧 معامل التعقيد / Complexity Factor:                                    │');
        file.writeln('│      ' + (complexityData.name + ' (×' + complexityData.multiplier + ')').padEnd(65) + '│');
        file.writeln('│                                                                             │');
        
        // التعديلات
        var revisions = parseInt(ui.revisionsInput.text);
        var freeRevisions = 2;
        var paidRevisions = Math.max(0, revisions - freeRevisions);
        var revisionCost = 15;
        if (typeof PricingConfig !== 'undefined' && PricingConfig.additionalCosts && PricingConfig.additionalCosts.revisionCost) {
            revisionCost = PricingConfig.additionalCosts.revisionCost;
        }
        
        file.writeln('│  ✏️ التعديلات / Revisions:                                                │');
        if (paidRevisions > 0) {
            var revisionsCost = paidRevisions * revisionCost;
            file.writeln('│      ' + ('تعديلات مجانية: ' + freeRevisions + ' / Free: ' + freeRevisions).padEnd(65) + '│');
            file.writeln('│      ' + ('تعديلات إضافية: ' + paidRevisions + ' × $' + revisionCost + ' = $' + revisionsCost).padEnd(65) + '│');
        } else {
            file.writeln('│      ' + ('جميع التعديلات مشمولة مجاناً / All revisions included').padEnd(65) + '│');
        }
        file.writeln('│                                                                             │');
        file.writeln('└─────────────────────────────────────────────────────────────────────────────┘');
        file.writeln('');
        file.writeln('');
        
        // السعر النهائي
        file.writeln('████████████████████████████████████████████████████████████████████████████████');
        file.writeln('█                                                                              █');
        file.writeln('█                          إجمالي المبلغ المستحق                          █');
        file.writeln('█                            TOTAL AMOUNT DUE                                 █');
        file.writeln('█                                                                              █');
        file.writeln('█                              ' + (ui.currencySymbol.text + Math.round(price)).padStart(20).padEnd(40) + '█');
        file.writeln('█                                                                              █');
        file.writeln('████████████████████████████████████████████████████████████████████████████████');
        file.writeln('');
        file.writeln('');
        
        // الشروط والأحكام
        file.writeln('┌─────────────────────────────────────────────────────────────────────────────┐');
        file.writeln('│                            الشروط والأحكام                               │');
        file.writeln('│                           TERMS & CONDITIONS                               │');
        file.writeln('├─────────────────────────────────────────────────────────────────────────────┤');
        file.writeln('│                                                                             │');
        file.writeln('│  📋 شروط الدفع / Payment Terms:                                           │');
        file.writeln('│      • يتطلب دفع 50% مقدماً لبدء العمل                                   │');
        file.writeln('│      • المبلغ المتبقي يُدفع عند التسليم النهائي                          │');
        file.writeln('│      • Requires 50% advance payment to start work                          │');
        file.writeln('│      • Remaining amount due upon final delivery                            │');
        file.writeln('│                                                                             │');
        file.writeln('│  ⏰ مدة الصلاحية / Validity Period:                                       │');
        file.writeln('│      • هذا العرض صالح لمدة 30 يوماً من تاريخ الإصدار                    │');
        file.writeln('│      • This offer is valid for 30 days from issue date                    │');
        file.writeln('│                                                                             │');
        file.writeln('│  🔄 التعديلات / Revisions:                                                │');
        file.writeln('│      • التعديلات الإضافية تتطلب موافقة مسبقة                             │');
        file.writeln('│      • Additional revisions require prior approval                         │');
        file.writeln('│                                                                             │');
        file.writeln('│  📅 التسليم / Delivery:                                                   │');
        file.writeln('│      • مدة التسليم تبدأ من تاريخ استلام المقدم                           │');
        file.writeln('│      • Delivery time starts from advance payment receipt                   │');
        file.writeln('│                                                                             │');
        file.writeln('│  ⚖️ حقوق الملكية / Ownership Rights:                                      │');
        file.writeln('│      • حقوق الملكية تنتقل للعميل عند السداد الكامل                       │');
        file.writeln('│      • Ownership rights transfer to client upon full payment              │');
        file.writeln('│                                                                             │');
        file.writeln('└─────────────────────────────────────────────────────────────────────────────┘');
        file.writeln('');
        file.writeln('');
        
        // معلومات الاتصال
        file.writeln('┌─────────────────────────────────────────────────────────────────────────────┐');
        file.writeln('│                            معلومات الاتصال                               │');
        file.writeln('│                           CONTACT INFORMATION                              │');
        file.writeln('├─────────────────────────────────────────────────────────────────────────────┤');
        file.writeln('│                                                                             │');
        file.writeln('│  📧 البريد الإلكتروني / Email: <EMAIL>                     │');
        file.writeln('│  📱 الهاتف / Phone: +966 50 123 4567                                      │');
        file.writeln('│  🌐 الموقع الإلكتروني / Website: www.designstudio.com                   │');
        file.writeln('│  📍 العنوان / Address: الرياض، المملكة العربية السعودية                │');
        file.writeln('│                                                                             │');
        file.writeln('└─────────────────────────────────────────────────────────────────────────────┘');
        file.writeln('');
        file.writeln('');
        
        // تذييل
        file.writeln('═══════════════════════════════════════════════════════════════════════════════');
        file.writeln('                تم إنشاؤه بواسطة حاسبة أسعار التصميم الاحترافية v2.0');
        file.writeln('              Generated by Design Price Calculator Pro v2.0');
        file.writeln('═══════════════════════════════════════════════════════════════════════════════');
        file.writeln('');
        file.writeln('شكراً لثقتكم بنا - Thank you for your trust');
        file.writeln('');
        
        file.close();
        
        alert('✅ تم إنشاء عقد التسعير الاحترافي بنجاح!\n\n' +
              '📄 الملف محفوظ في:\n' + file.fsName + '\n\n' +
              '🎯 العقد يحتوي على:\n' +
              '• تنسيق احترافي بخطوط كبيرة وواضحة\n' +
              '• تفاصيل المشروع الكاملة\n' +
              '• تفاصيل التسعير المفصلة\n' +
              '• الشروط والأحكام الواضحة\n' +
              '• معلومات الاتصال\n' +
              '• تصميم جاهز للطباعة والإرسال\n\n' +
              '💡 يمكنك فتح الملف في أي محرر نصوص وتحويله إلى PDF');
        
        return true;
        
    } catch (error) {
        alert('❌ خطأ في إنشاء العقد: ' + error.message);
        return false;
    }
}

// دالة تصدير PDF محسنة
function exportToPDF(ui, price) {
    try {
        var choice = confirm('📄 هل تريد إنشاء عقد تسعير احترافي؟\n\n' +
                           '🎨 سيتم إنشاء عقد منسق بـ:\n' +
                           '• خطوط كبيرة وواضحة\n' +
                           '• تنسيق احترافي منظم\n' +
                           '• تفاصيل شاملة\n' +
                           '• شروط وأحكام واضحة\n' +
                           '• تصميم جاهز للطباعة\n\n' +
                           'الملف سيكون بصيغة نصية يمكن تحويلها إلى PDF');
        
        if (choice) {
            return createProfessionalContract(ui, price);
        }
        
        return false;
        
    } catch (error) {
        alert('❌ خطأ في تصدير العقد: ' + error.message);
        return false;
    }
}
