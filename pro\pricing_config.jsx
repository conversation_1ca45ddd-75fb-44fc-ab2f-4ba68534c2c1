/*
إعدادات التسعير - Pricing Configuration
ملف منفصل لإعدادات التسعير لسهولة التخصيص والتعديل
*/

// إضافة دعم Object.keys للإصدارات القديمة من ExtendScript
if (!Object.keys) {
    Object.keys = function(obj) {
        var keys = [];
        for (var key in obj) {
            if (obj.hasOwnProperty(key)) {
                keys.push(key);
            }
        }
        return keys;
    };
}

// كائن إعدادات التسعير الرئيسي
var PricingConfig = {
    
    // معلومات عامة
    info: {
        version: "1.0",
        currency: "USD", // العملة المستخدمة
        lastUpdated: "2024-12-18"
    },
    
    // أنواع التصميم والأسعار الأساسية
    designTypes: {
        'logo': {
            name: 'لوجو',
            nameEn: 'Logo',
            basePrice: 50,          // السعر الأساسي بالدولار/ساعة
            complexity: 1.5,        // معامل التعقيد الافتراضي
            minHours: 3,           // الحد الأدنى للساعات
            maxHours: 20,          // الحد الأقصى للساعات
            description: 'تصميم شعار احترافي'
        },
        'social_media': {
            name: 'سوشيال ميديا',
            nameEn: 'Social Media',
            basePrice: 25,
            complexity: 1.0,
            minHours: 1,
            maxHours: 8,
            description: 'تصميم منشورات وسائل التواصل الاجتماعي'
        },
        'brand_identity': {
            name: 'هوية بصرية',
            nameEn: 'Brand Identity',
            basePrice: 75,
            complexity: 2.0,
            minHours: 10,
            maxHours: 50,
            description: 'تصميم هوية بصرية متكاملة'
        },
        'poster': {
            name: 'بوستر',
            nameEn: 'Poster',
            basePrice: 40,
            complexity: 1.3,
            minHours: 2,
            maxHours: 12,
            description: 'تصميم بوستر إعلاني أو فني'
        },
        'ui_ux': {
            name: 'UI/UX',
            nameEn: 'UI/UX Design',
            basePrice: 60,
            complexity: 1.8,
            minHours: 5,
            maxHours: 40,
            description: 'تصميم واجهات المستخدم وتجربة المستخدم'
        },
        'business_card': {
            name: 'كارت شخصي',
            nameEn: 'Business Card',
            basePrice: 30,
            complexity: 1.0,
            minHours: 1,
            maxHours: 4,
            description: 'تصميم كارت شخصي أو بطاقة عمل'
        },
        'brochure': {
            name: 'بروشور',
            nameEn: 'Brochure',
            basePrice: 45,
            complexity: 1.4,
            minHours: 3,
            maxHours: 15,
            description: 'تصميم بروشور أو مطوية إعلانية'
        },
        'packaging': {
            name: 'تصميم عبوات',
            nameEn: 'Packaging Design',
            basePrice: 80,
            complexity: 2.2,
            minHours: 8,
            maxHours: 30,
            description: 'تصميم عبوات المنتجات'
        },
        'web_banner': {
            name: 'بانر ويب',
            nameEn: 'Web Banner',
            basePrice: 35,
            complexity: 1.1,
            minHours: 1,
            maxHours: 6,
            description: 'تصميم بانرات للمواقع الإلكترونية'
        },
        'infographic': {
            name: 'إنفوجرافيك',
            nameEn: 'Infographic',
            basePrice: 55,
            complexity: 1.6,
            minHours: 4,
            maxHours: 16,
            description: 'تصميم إنفوجرافيك تفاعلي'
        }
    },
    
    // درجات التعقيد ومعاملاتها
    complexityMultipliers: {
        'simple': {
            name: 'بسيط',
            nameEn: 'Simple',
            multiplier: 1.0,
            description: 'تصميم بسيط بعناصر قليلة'
        },
        'medium': {
            name: 'متوسط',
            nameEn: 'Medium',
            multiplier: 1.5,
            description: 'تصميم متوسط التعقيد'
        },
        'complex': {
            name: 'معقد',
            nameEn: 'Complex',
            multiplier: 2.0,
            description: 'تصميم معقد بتفاصيل كثيرة'
        },
        'very_complex': {
            name: 'معقد جداً',
            nameEn: 'Very Complex',
            multiplier: 2.5,
            description: 'تصميم شديد التعقيد'
        }
    },
    
    // أنواع الاستخدام ومعاملاتها
    usageMultipliers: {
        'personal': {
            name: 'شخصي',
            nameEn: 'Personal',
            multiplier: 1.0,
            description: 'استخدام شخصي غير تجاري'
        },
        'small_business': {
            name: 'مشروع صغير',
            nameEn: 'Small Business',
            multiplier: 1.4,
            description: 'مشروع صغير أو شركة ناشئة'
        },
        'commercial': {
            name: 'تجاري',
            nameEn: 'Commercial',
            multiplier: 1.8,
            description: 'استخدام تجاري عام'
        },
        'enterprise': {
            name: 'مؤسسي',
            nameEn: 'Enterprise',
            multiplier: 2.5,
            description: 'شركة كبيرة أو مؤسسة'
        }
    },
    
    // سرعة التسليم ومعاملاتها
    deliveryMultipliers: {
        'normal': {
            name: 'عادي',
            nameEn: 'Normal',
            multiplier: 1.0,
            description: 'تسليم في الوقت العادي (3-7 أيام)'
        },
        'fast': {
            name: 'سريع',
            nameEn: 'Fast',
            multiplier: 1.3,
            description: 'تسليم سريع (1-2 يوم)'
        },
        'urgent': {
            name: 'مستعجل',
            nameEn: 'Urgent',
            multiplier: 1.5,
            description: 'تسليم مستعجل (نفس اليوم)'
        },
        'express': {
            name: 'فوري',
            nameEn: 'Express',
            multiplier: 2.0,
            description: 'تسليم فوري (خلال ساعات)'
        }
    },
    
    // تكاليف إضافية
    additionalCosts: {
        revisionCost: 15,           // تكلفة كل تعديل
        consultationHour: 40,       // تكلفة ساعة استشارة
        conceptFee: 25,             // رسم كل مفهوم إضافي
        sourceFilesFee: 20,         // رسم تسليم الملفات المصدرية
        rushJobFee: 50,             // رسم إضافي للأعمال المستعجلة
        weekendWorkFee: 30          // رسم العمل في نهاية الأسبوع
    },
    
    // خصومات
    discounts: {
        bulkDiscount: {
            threshold: 5,           // عدد المشاريع للحصول على خصم
            percentage: 10          // نسبة الخصم بالمئة
        },
        loyalClientDiscount: {
            percentage: 15          // خصم العملاء المميزين
        },
        studentDiscount: {
            percentage: 20          // خصم الطلاب
        }
    },
    
    // إعدادات عامة
    settings: {
        minProjectValue: 25,        // الحد الأدنى لقيمة المشروع
        maxProjectValue: 5000,      // الحد الأقصى لقيمة المشروع
        defaultHours: 5,            // عدد الساعات الافتراضي
        defaultRevisions: 2,        // عدد التعديلات الافتراضي
        taxRate: 0,                 // معدل الضريبة (0 = بدون ضريبة)
        profitMargin: 1.2           // هامش الربح
    },
    
    // رسائل النظام
    messages: {
        welcome: 'مرحباً بك في حاسبة أسعار التصميم',
        calculationComplete: 'تم حساب السعر بنجاح',
        saveSuccess: 'تم حفظ التقدير بنجاح',
        saveError: 'خطأ في حفظ الملف',
        invalidInput: 'يرجى التحقق من البيانات المدخلة',
        priceUpdated: 'تم تحديث السعر'
    }
};

// دالة للحصول على قائمة أنواع التصميم
function getDesignTypesList() {
    var list = [];
    for (var key in PricingConfig.designTypes) {
        list.push({
            key: key,
            name: PricingConfig.designTypes[key].name,
            nameEn: PricingConfig.designTypes[key].nameEn
        });
    }
    return list;
}

// دالة للحصول على معلومات نوع تصميم محدد
function getDesignTypeInfo(key) {
    return PricingConfig.designTypes[key] || null;
}

// دالة لتحديث سعر نوع تصميم
function updateDesignTypePrice(key, newPrice) {
    if (PricingConfig.designTypes[key]) {
        PricingConfig.designTypes[key].basePrice = newPrice;
        return true;
    }
    return false;
}

// دالة للحصول على معامل التعقيد
function getComplexityMultiplier(level) {
    var keys = Object.keys(PricingConfig.complexityMultipliers);
    var index = Math.max(0, Math.min(level - 1, keys.length - 1));
    return PricingConfig.complexityMultipliers[keys[index]];
}

// دالة للحصول على معامل الاستخدام
function getUsageMultiplier(type) {
    return PricingConfig.usageMultipliers[type] || PricingConfig.usageMultipliers['personal'];
}

// دالة للحصول على معامل التسليم
function getDeliveryMultiplier(type) {
    return PricingConfig.deliveryMultipliers[type] || PricingConfig.deliveryMultipliers['normal'];
}

// دالة لحفظ الإعدادات (للاستخدام المستقبلي)
function saveConfig() {
    // يمكن تطوير هذه الدالة لحفظ الإعدادات في ملف خارجي
    return true;
}

// دالة لتحميل الإعدادات (للاستخدام المستقبلي)
function loadConfig() {
    // يمكن تطوير هذه الدالة لتحميل الإعدادات من ملف خارجي
    return PricingConfig;
}
