/*
الميزات المتقدمة - Advanced Features
ملف يحتوي على الميزات المتقدمة لحاسبة أسعار التصميم
*/

// إضافة دعم Object.keys للإصدارات القديمة من ExtendScript
if (!Object.keys) {
    Object.keys = function(obj) {
        var keys = [];
        for (var key in obj) {
            if (obj.hasOwnProperty(key)) {
                keys.push(key);
            }
        }
        return keys;
    };
}

// إعدادات الميزات المتقدمة
var AdvancedConfig = {
    // العملات المدعومة
    currencies: {
        'USD': { symbol: '$', name: 'US Dollar', nameAr: 'دولار أمريكي', rate: 1.0 },
        'EUR': { symbol: '€', name: 'Euro', nameAr: 'يورو', rate: 0.85 },
        'GBP': { symbol: '£', name: 'British Pound', nameAr: 'جنيه إسترليني', rate: 0.75 },
        'SAR': { symbol: 'ر.س', name: 'Saudi Riyal', nameAr: 'ريال سعودي', rate: 3.75 },
        'AED': { symbol: 'د.إ', name: 'UAE Dirham', nameAr: 'درهم إماراتي', rate: 3.67 },
        'EGP': { symbol: 'ج.م', name: 'Egyptian Pound', nameAr: 'جنيه مصري', rate: 30.9 }
    },
    
    // إعدادات التحليل التلقائي
    autoAnalysis: {
        layerComplexity: {
            simple: { min: 1, max: 5, multiplier: 1.0 },
            medium: { min: 6, max: 15, multiplier: 1.3 },
            complex: { min: 16, max: 30, multiplier: 1.6 },
            veryComplex: { min: 31, max: 999, multiplier: 2.0 }
        },
        smartObjectBonus: 0.2, // 20% إضافي لكل Smart Object
        effectsBonus: 0.1 // 10% إضافي لكل تأثير
    },
    

    
    // سجل المشاريع
    projectHistory: [],
    
    // إعدادات العميل
    clientNotes: '',
    clientName: '',
    designerLogo: '',
    
    // الثيم
    currentTheme: 'light' // light, dark
};

// دالة تحليل ملف PSD/AI (محاكاة)
function analyzeDesignFile() {
    try {
        // محاولة الحصول على معلومات الوثيقة النشطة
        if (app.documents.length === 0) {
            alert('⚠️ يرجى فتح ملف PSD أو AI أولاً');
            return null;
        }
        
        var doc = app.activeDocument;
        var analysis = {
            fileName: doc.name,
            layers: 0,
            smartObjects: 0,
            effects: 0,
            complexity: 'simple',
            suggestedHours: 3
        };
        
        // عد الطبقات (تقريبي)
        try {
            analysis.layers = doc.layers.length;
            
            // تحليل مستوى التعقيد بناءً على عدد الطبقات
            if (analysis.layers <= 5) {
                analysis.complexity = 'simple';
                analysis.suggestedHours = 2 + (analysis.layers * 0.5);
            } else if (analysis.layers <= 15) {
                analysis.complexity = 'medium';
                analysis.suggestedHours = 4 + (analysis.layers * 0.3);
            } else if (analysis.layers <= 30) {
                analysis.complexity = 'complex';
                analysis.suggestedHours = 8 + (analysis.layers * 0.2);
            } else {
                analysis.complexity = 'very_complex';
                analysis.suggestedHours = 15 + (analysis.layers * 0.1);
            }
            
        } catch (e) {
            // في حالة عدم القدرة على الوصول للطبقات
            analysis.layers = 'غير محدد';
            analysis.suggestedHours = 5;
        }
        
        return analysis;
        
    } catch (error) {
        alert('❌ خطأ في تحليل الملف: ' + error.message);
        return null;
    }
}

// دالة عرض نتائج التحليل التلقائي
function showAnalysisResults(analysis) {
    if (!analysis) return;
    
    var dialog = new Window('dialog', 'نتائج التحليل التلقائي - Auto Analysis Results');
    dialog.orientation = 'column';
    dialog.alignChildren = 'fill';
    dialog.spacing = 15;
    dialog.margins = 20;
    dialog.preferredSize.width = 450;
    
    // عنوان
    var title = dialog.add('statictext', undefined, '🧠 تحليل الملف التلقائي');
    title.graphics.font = ScriptUI.newFont(title.graphics.font.name, ScriptUI.FontStyle.BOLD, 16);
    
    // معلومات الملف
    var infoGroup = dialog.add('panel', undefined, 'معلومات الملف');
    infoGroup.orientation = 'column';
    infoGroup.alignChildren = 'fill';
    infoGroup.margins = 15;
    
    infoGroup.add('statictext', undefined, '📄 اسم الملف: ' + analysis.fileName);
    infoGroup.add('statictext', undefined, '📚 عدد الطبقات: ' + analysis.layers);
    infoGroup.add('statictext', undefined, '🔧 مستوى التعقيد: ' + analysis.complexity);
    infoGroup.add('statictext', undefined, '⏰ الوقت المقترح: ' + Math.round(analysis.suggestedHours) + ' ساعة');
    
    // أزرار التحكم
    var buttonGroup = dialog.add('group');
    buttonGroup.orientation = 'row';
    buttonGroup.alignChildren = 'center';
    
    var applyButton = buttonGroup.add('button', undefined, '✅ تطبيق النتائج');
    var cancelButton = buttonGroup.add('button', undefined, '❌ إلغاء');
    
    var result = { apply: false, hours: analysis.suggestedHours, complexity: analysis.complexity };
    
    applyButton.onClick = function() {
        result.apply = true;
        dialog.close();
    };
    
    cancelButton.onClick = function() {
        dialog.close();
    };
    
    dialog.show();
    return result;
}

// دالة إدارة العملات
function showCurrencyManager() {
    var dialog = new Window('dialog', 'إدارة العملات - Currency Manager');
    dialog.orientation = 'column';
    dialog.alignChildren = 'fill';
    dialog.spacing = 15;
    dialog.margins = 20;
    dialog.preferredSize.width = 400;
    
    var title = dialog.add('statictext', undefined, '🌍 اختر العملة');
    title.graphics.font = ScriptUI.newFont(title.graphics.font.name, ScriptUI.FontStyle.BOLD, 14);
    
    var currencyGroup = dialog.add('panel', undefined, 'العملات المتاحة');
    currencyGroup.orientation = 'column';
    currencyGroup.alignChildren = 'fill';
    currencyGroup.margins = 15;
    
    var currencyRadios = {};
    var selectedCurrency = 'USD';
    
    for (var code in AdvancedConfig.currencies) {
        var currency = AdvancedConfig.currencies[code];
        var radio = currencyGroup.add('radiobutton', undefined, 
            currency.symbol + ' ' + currency.nameAr + ' (' + currency.name + ')');
        
        if (code === 'USD') radio.value = true;
        
        currencyRadios[code] = radio;
        
        // إضافة حدث للاختيار
        (function(currencyCode) {
            radio.onClick = function() {
                selectedCurrency = currencyCode;
            };
        })(code);
    }
    
    // معدل التحويل
    var rateGroup = dialog.add('group');
    rateGroup.add('statictext', undefined, 'معدل التحويل من USD:');
    var rateDisplay = rateGroup.add('statictext', undefined, '1.00');
    
    // تحديث معدل التحويل عند تغيير العملة
    for (var code in currencyRadios) {
        (function(currencyCode) {
            currencyRadios[currencyCode].onClick = function() {
                selectedCurrency = currencyCode;
                rateDisplay.text = AdvancedConfig.currencies[currencyCode].rate.toString();
            };
        })(code);
    }
    
    var buttonGroup = dialog.add('group');
    buttonGroup.orientation = 'row';
    buttonGroup.alignChildren = 'center';
    
    var okButton = buttonGroup.add('button', undefined, '✅ تطبيق');
    var cancelButton = buttonGroup.add('button', undefined, '❌ إلغاء');
    
    var result = { selected: false, currency: 'USD' };
    
    okButton.onClick = function() {
        result.selected = true;
        result.currency = selectedCurrency;
        dialog.close();
    };
    
    cancelButton.onClick = function() {
        dialog.close();
    };
    
    dialog.show();
    return result;
}



// دالة إدارة ملاحظات العميل
function manageClientNotes() {
    var dialog = new Window('dialog', 'ملاحظات العميل - Client Notes');
    dialog.orientation = 'column';
    dialog.alignChildren = 'fill';
    dialog.spacing = 15;
    dialog.margins = 20;
    dialog.preferredSize.width = 500;
    
    var title = dialog.add('statictext', undefined, '💬 ملاحظات العميل');
    title.graphics.font = ScriptUI.newFont(title.graphics.font.name, ScriptUI.FontStyle.BOLD, 14);
    
    // اسم العميل
    var clientGroup = dialog.add('group');
    clientGroup.add('statictext', undefined, 'اسم العميل:').preferredSize.width = 80;
    var clientNameInput = clientGroup.add('edittext', undefined, AdvancedConfig.clientName);
    clientNameInput.preferredSize.width = 300;
    
    // الملاحظات
    var notesGroup = dialog.add('panel', undefined, 'الملاحظات');
    notesGroup.orientation = 'column';
    notesGroup.alignChildren = 'fill';
    notesGroup.margins = 15;
    
    var notesInput = notesGroup.add('edittext', undefined, AdvancedConfig.clientNotes, {multiline: true});
    notesInput.preferredSize.height = 150;
    notesInput.preferredSize.width = 450;
    
    // قوالب الملاحظات
    var templatesGroup = notesGroup.add('group');
    templatesGroup.add('statictext', undefined, 'قوالب سريعة:');
    
    var template1 = templatesGroup.add('button', undefined, 'متطلبات أساسية');
    var template2 = templatesGroup.add('button', undefined, 'تعديلات إضافية');
    var template3 = templatesGroup.add('button', undefined, 'شروط الدفع');
    
    // أحداث القوالب
    template1.onClick = function() {
        notesInput.text += '\n• يشمل السعر التصميم الأساسي فقط\n• ملفات التسليم: AI, PSD, PNG, JPG\n• مدة التسليم: 3-5 أيام عمل';
    };
    
    template2.onClick = function() {
        notesInput.text += '\n• التعديلات الإضافية: $15 لكل تعديل\n• التعديلات الجوهرية تتطلب تسعير منفصل\n• الحد الأقصى للتعديلات: 5 تعديلات';
    };
    
    template3.onClick = function() {
        notesInput.text += '\n• الدفع: 50% مقدم، 50% عند التسليم\n• تسليم الملفات بعد استلام كامل المبلغ\n• إلغاء المشروع: استرداد 25% فقط';
    };
    
    var buttonGroup = dialog.add('group');
    buttonGroup.orientation = 'row';
    buttonGroup.alignChildren = 'center';
    
    var saveButton = buttonGroup.add('button', undefined, '💾 حفظ');
    var cancelButton = buttonGroup.add('button', undefined, '❌ إلغاء');
    
    var result = { saved: false, clientName: '', notes: '' };
    
    saveButton.onClick = function() {
        AdvancedConfig.clientName = clientNameInput.text;
        AdvancedConfig.clientNotes = notesInput.text;
        result.saved = true;
        result.clientName = clientNameInput.text;
        result.notes = notesInput.text;
        dialog.close();
    };
    
    cancelButton.onClick = function() {
        dialog.close();
    };
    
    dialog.show();
    return result;
}

// دالة عرض سجل المشاريع
function showProjectHistory() {
    var dialog = new Window('dialog', 'سجل المشاريع - Project History');
    dialog.orientation = 'column';
    dialog.alignChildren = 'fill';
    dialog.spacing = 15;
    dialog.margins = 20;
    dialog.preferredSize.width = 600;
    dialog.preferredSize.height = 400;
    
    var title = dialog.add('statictext', undefined, '🧾 سجل المشاريع السابقة');
    title.graphics.font = ScriptUI.newFont(title.graphics.font.name, ScriptUI.FontStyle.BOLD, 14);
    
    // قائمة المشاريع
    var listGroup = dialog.add('panel', undefined, 'المشاريع المحفوظة');
    listGroup.orientation = 'column';
    listGroup.alignChildren = 'fill';
    listGroup.margins = 15;
    
    var projectList = listGroup.add('listbox');
    projectList.preferredSize.height = 250;
    
    // إضافة المشاريع للقائمة (مثال)
    if (AdvancedConfig.projectHistory.length === 0) {
        projectList.add('item', 'لا توجد مشاريع محفوظة');
    } else {
        for (var i = 0; i < AdvancedConfig.projectHistory.length; i++) {
            var project = AdvancedConfig.projectHistory[i];
            projectList.add('item', project.date + ' - ' + project.type + ' - $' + project.price);
        }
    }
    
    var buttonGroup = dialog.add('group');
    buttonGroup.orientation = 'row';
    buttonGroup.alignChildren = 'center';
    
    var loadButton = buttonGroup.add('button', undefined, '📂 تحميل مشروع');
    var deleteButton = buttonGroup.add('button', undefined, '🗑️ حذف');
    var closeButton = buttonGroup.add('button', undefined, '❌ إغلاق');
    
    loadButton.enabled = false;
    deleteButton.enabled = false;
    
    projectList.onChange = function() {
        loadButton.enabled = this.selection !== null;
        deleteButton.enabled = this.selection !== null;
    };
    
    loadButton.onClick = function() {
        if (projectList.selection) {
            alert('ميزة تحميل المشروع ستكون متاحة في الإصدار القادم');
        }
    };
    
    deleteButton.onClick = function() {
        if (projectList.selection) {
            var result = confirm('هل أنت متأكد من حذف هذا المشروع؟');
            if (result) {
                projectList.remove(projectList.selection.index);
                loadButton.enabled = false;
                deleteButton.enabled = false;
            }
        }
    };
    
    closeButton.onClick = function() {
        dialog.close();
    };
    
    dialog.show();
}
