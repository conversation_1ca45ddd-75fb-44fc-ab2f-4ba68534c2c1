/*
مخصص الأسعار المتقدم - Advanced Price Customizer
أداة لتخصيص وتعديل إعدادات التسعير بسهولة مع ميزات متقدمة
*/

// إضافة دعم Object.keys للإصدارات القديمة من ExtendScript
if (!Object.keys) {
    Object.keys = function(obj) {
        var keys = [];
        for (var key in obj) {
            if (obj.hasOwnProperty(key)) {
                keys.push(key);
            }
        }
        return keys;
    };
}

// إعدادات التخصيص المتقدمة
var CustomizationConfig = {
    // مستويات الخبرة
    experienceLevels: {
        beginner: { name: 'مبتدئ', nameEn: 'Beginner', multiplier: 0.7, minRate: 10, maxRate: 25 },
        intermediate: { name: 'متوسط', nameEn: 'Intermediate', multiplier: 1.0, minRate: 25, maxRate: 50 },
        advanced: { name: 'متقدم', nameEn: 'Advanced', multiplier: 1.3, minRate: 50, maxRate: 100 },
        expert: { name: 'خب<PERSON><PERSON>', nameEn: 'Expert', multiplier: 1.6, minRate: 100, maxRate: 200 },
        master: { name: 'أستاذ', nameEn: 'Master', multiplier: 2.0, minRate: 200, maxRate: 500 }
    },

    // أنواع العملاء
    clientTypes: {
        individual: { name: 'فرد', nameEn: 'Individual', multiplier: 1.0 },
        startup: { name: 'شركة ناشئة', nameEn: 'Startup', multiplier: 1.1 },
        smallBusiness: { name: 'شركة صغيرة', nameEn: 'Small Business', multiplier: 1.2 },
        corporation: { name: 'شركة كبيرة', nameEn: 'Corporation', multiplier: 1.5 },
        agency: { name: 'وكالة إعلانية', nameEn: 'Agency', multiplier: 1.3 },
        nonprofit: { name: 'منظمة غير ربحية', nameEn: 'Non-profit', multiplier: 0.8 }
    },

    // المناطق الجغرافية
    regions: {
        local: { name: 'محلي', nameEn: 'Local', multiplier: 1.0 },
        regional: { name: 'إقليمي', nameEn: 'Regional', multiplier: 1.2 },
        national: { name: 'وطني', nameEn: 'National', multiplier: 1.4 },
        international: { name: 'دولي', nameEn: 'International', multiplier: 1.8 }
    },

    // إعدادات المستخدم المحفوظة
    userSettings: {
        experienceLevel: 'intermediate',
        baseHourlyRate: 35,
        preferredCurrency: 'USD',
        defaultClientType: 'individual',
        defaultRegion: 'local',
        customRates: {}
    }
};

// دالة إنشاء واجهة تخصيص الأسعار
function createCustomizerUI() {
    var dialog = new Window('dialog', 'مخصص الأسعار - Price Customizer');
    dialog.orientation = 'column';
    dialog.alignChildren = 'fill';
    dialog.spacing = 10;
    dialog.margins = 20;
    dialog.preferredSize.width = 600;
    dialog.preferredSize.height = 500;
    
    // عنوان
    var headerGroup = dialog.add('panel');
    headerGroup.orientation = 'row';
    headerGroup.alignChildren = 'center';
    headerGroup.margins = 10;
    
    var title = headerGroup.add('statictext', undefined, '⚙️ مخصص إعدادات التسعير');
    title.graphics.font = ScriptUI.newFont(title.graphics.font.name, ScriptUI.FontStyle.BOLD, 16);
    
    // تبويبات
    var tabPanel = dialog.add('tabbedpanel');
    tabPanel.alignChildren = 'fill';
    tabPanel.preferredSize.height = 400;
    
    // تبويب أنواع التصميم
    var designTypesTab = tabPanel.add('tab', undefined, 'أنواع التصميم');
    designTypesTab.orientation = 'column';
    designTypesTab.alignChildren = 'fill';
    
    var designTypesGroup = designTypesTab.add('group');
    designTypesGroup.orientation = 'row';
    designTypesGroup.alignChildren = 'fill';
    
    // قائمة أنواع التصميم
    var designTypesList = designTypesGroup.add('listbox');
    designTypesList.preferredSize.width = 200;
    
    // إضافة أنواع التصميم إلى القائمة
    for (var key in PricingConfig.designTypes) {
        var item = PricingConfig.designTypes[key];
        designTypesList.add('item', item.name + ' - $' + item.basePrice);
    }
    
    // لوحة تعديل نوع التصميم
    var editDesignGroup = designTypesGroup.add('panel', undefined, 'تعديل النوع المختار');
    editDesignGroup.orientation = 'column';
    editDesignGroup.alignChildren = 'fill';
    editDesignGroup.margins = 10;
    
    var nameRow = editDesignGroup.add('group');
    nameRow.add('statictext', undefined, 'الاسم:').preferredSize.width = 80;
    var nameInput = nameRow.add('edittext');
    nameInput.preferredSize.width = 150;
    
    var priceRow = editDesignGroup.add('group');
    priceRow.add('statictext', undefined, 'السعر/ساعة:').preferredSize.width = 80;
    var priceInput = priceRow.add('edittext');
    priceInput.preferredSize.width = 100;
    
    var descRow = editDesignGroup.add('group');
    descRow.orientation = 'column';
    descRow.add('statictext', undefined, 'الوصف:');
    var descInput = descRow.add('edittext', undefined, '', {multiline: true});
    descInput.preferredSize.height = 60;
    
    var updateDesignButton = editDesignGroup.add('button', undefined, 'تحديث');
    
    // تبويب معاملات التعقيد
    var complexityTab = tabPanel.add('tab', undefined, 'معاملات التعقيد');
    complexityTab.orientation = 'column';
    complexityTab.alignChildren = 'fill';
    
    var complexityGroup = complexityTab.add('panel', undefined, 'معاملات درجة التعقيد');
    complexityGroup.orientation = 'column';
    complexityGroup.alignChildren = 'fill';
    complexityGroup.margins = 15;
    
    var complexityInputs = {};
    var complexityKeys = Object.keys(PricingConfig.complexityMultipliers);
    
    for (var i = 0; i < complexityKeys.length; i++) {
        var key = complexityKeys[i];
        var complexity = PricingConfig.complexityMultipliers[key];
        
        var row = complexityGroup.add('group');
        row.add('statictext', undefined, complexity.name + ':').preferredSize.width = 100;
        var input = row.add('edittext', undefined, complexity.multiplier.toString());
        input.preferredSize.width = 80;
        row.add('statictext', undefined, 'x');
        
        complexityInputs[key] = input;
    }
    
    var updateComplexityButton = complexityGroup.add('button', undefined, 'تحديث معاملات التعقيد');
    
    // تبويب معاملات الاستخدام
    var usageTab = tabPanel.add('tab', undefined, 'معاملات الاستخدام');
    usageTab.orientation = 'column';
    usageTab.alignChildren = 'fill';
    
    var usageGroup = usageTab.add('panel', undefined, 'معاملات نوع الاستخدام');
    usageGroup.orientation = 'column';
    usageGroup.alignChildren = 'fill';
    usageGroup.margins = 15;
    
    var usageInputs = {};
    var usageKeys = Object.keys(PricingConfig.usageMultipliers);
    
    for (var i = 0; i < usageKeys.length; i++) {
        var key = usageKeys[i];
        var usage = PricingConfig.usageMultipliers[key];
        
        var row = usageGroup.add('group');
        row.add('statictext', undefined, usage.name + ':').preferredSize.width = 100;
        var input = row.add('edittext', undefined, usage.multiplier.toString());
        input.preferredSize.width = 80;
        row.add('statictext', undefined, 'x');
        
        usageInputs[key] = input;
    }
    
    var updateUsageButton = usageGroup.add('button', undefined, 'تحديث معاملات الاستخدام');
    
    // تبويب معاملات التسليم
    var deliveryTab = tabPanel.add('tab', undefined, 'معاملات التسليم');
    deliveryTab.orientation = 'column';
    deliveryTab.alignChildren = 'fill';
    
    var deliveryGroup = deliveryTab.add('panel', undefined, 'معاملات سرعة التسليم');
    deliveryGroup.orientation = 'column';
    deliveryGroup.alignChildren = 'fill';
    deliveryGroup.margins = 15;
    
    var deliveryInputs = {};
    var deliveryKeys = Object.keys(PricingConfig.deliveryMultipliers);
    
    for (var i = 0; i < deliveryKeys.length; i++) {
        var key = deliveryKeys[i];
        var delivery = PricingConfig.deliveryMultipliers[key];
        
        var row = deliveryGroup.add('group');
        row.add('statictext', undefined, delivery.name + ':').preferredSize.width = 100;
        var input = row.add('edittext', undefined, delivery.multiplier.toString());
        input.preferredSize.width = 80;
        row.add('statictext', undefined, 'x');
        
        deliveryInputs[key] = input;
    }
    
    var updateDeliveryButton = deliveryGroup.add('button', undefined, 'تحديث معاملات التسليم');
    
    // تبويب التكاليف الإضافية
    var additionalTab = tabPanel.add('tab', undefined, 'التكاليف الإضافية');
    additionalTab.orientation = 'column';
    additionalTab.alignChildren = 'fill';
    
    var additionalGroup = additionalTab.add('panel', undefined, 'التكاليف والرسوم الإضافية');
    additionalGroup.orientation = 'column';
    additionalGroup.alignChildren = 'fill';
    additionalGroup.margins = 15;
    
    var revisionRow = additionalGroup.add('group');
    revisionRow.add('statictext', undefined, 'تكلفة التعديل:').preferredSize.width = 120;
    var revisionInput = revisionRow.add('edittext', undefined, PricingConfig.revisionCost.toString());
    revisionInput.preferredSize.width = 80;
    revisionRow.add('statictext', undefined, '$');
    
    var consultationRow = additionalGroup.add('group');
    consultationRow.add('statictext', undefined, 'ساعة استشارة:').preferredSize.width = 120;
    var consultationInput = consultationRow.add('edittext', undefined, PricingConfig.additionalCosts.consultationHour.toString());
    consultationInput.preferredSize.width = 80;
    consultationRow.add('statictext', undefined, '$');
    
    var sourceFilesRow = additionalGroup.add('group');
    sourceFilesRow.add('statictext', undefined, 'الملفات المصدرية:').preferredSize.width = 120;
    var sourceFilesInput = sourceFilesRow.add('edittext', undefined, PricingConfig.additionalCosts.sourceFilesFee.toString());
    sourceFilesInput.preferredSize.width = 80;
    sourceFilesRow.add('statictext', undefined, '$');
    
    var updateAdditionalButton = additionalGroup.add('button', undefined, 'تحديث التكاليف الإضافية');
    
    // أزرار التحكم الرئيسية
    var mainButtonGroup = dialog.add('group');
    mainButtonGroup.orientation = 'row';
    mainButtonGroup.alignChildren = 'center';
    mainButtonGroup.spacing = 15;
    
    var saveConfigButton = mainButtonGroup.add('button', undefined, '💾 حفظ الإعدادات');
    var loadConfigButton = mainButtonGroup.add('button', undefined, '📂 تحميل إعدادات');
    var resetConfigButton = mainButtonGroup.add('button', undefined, '🔄 إعادة تعيين');
    var closeButton = mainButtonGroup.add('button', undefined, '❌ إغلاق');
    
    // ربط الأحداث
    
    // تحديث حقول التعديل عند اختيار نوع تصميم
    designTypesList.onChange = function() {
        if (this.selection) {
            var keys = Object.keys(PricingConfig.designTypes);
            var selectedKey = keys[this.selection.index];
            var selectedType = PricingConfig.designTypes[selectedKey];
            
            nameInput.text = selectedType.name;
            priceInput.text = selectedType.basePrice.toString();
            descInput.text = selectedType.description || '';
        }
    };
    
    // تحديث نوع التصميم
    updateDesignButton.onClick = function() {
        if (designTypesList.selection) {
            var keys = Object.keys(PricingConfig.designTypes);
            var selectedKey = keys[designTypesList.selection.index];
            
            var newPrice = parseFloat(priceInput.text);
            if (isNaN(newPrice) || newPrice <= 0) {
                alert('يرجى إدخال سعر صحيح');
                return;
            }
            
            PricingConfig.designTypes[selectedKey].name = nameInput.text;
            PricingConfig.designTypes[selectedKey].basePrice = newPrice;
            PricingConfig.designTypes[selectedKey].description = descInput.text;
            
            // تحديث القائمة
            designTypesList.remove(designTypesList.selection.index);
            var newItem = designTypesList.add('item', nameInput.text + ' - $' + newPrice);
            newItem.selected = true;
            
            alert('تم تحديث نوع التصميم بنجاح');
        }
    };
    
    // تحديث معاملات التعقيد
    updateComplexityButton.onClick = function() {
        for (var key in complexityInputs) {
            var value = parseFloat(complexityInputs[key].text);
            if (!isNaN(value) && value > 0) {
                PricingConfig.complexityMultipliers[key].multiplier = value;
            }
        }
        alert('تم تحديث معاملات التعقيد بنجاح');
    };
    
    // تحديث معاملات الاستخدام
    updateUsageButton.onClick = function() {
        for (var key in usageInputs) {
            var value = parseFloat(usageInputs[key].text);
            if (!isNaN(value) && value > 0) {
                PricingConfig.usageMultipliers[key].multiplier = value;
            }
        }
        alert('تم تحديث معاملات الاستخدام بنجاح');
    };
    
    // تحديث معاملات التسليم
    updateDeliveryButton.onClick = function() {
        for (var key in deliveryInputs) {
            var value = parseFloat(deliveryInputs[key].text);
            if (!isNaN(value) && value > 0) {
                PricingConfig.deliveryMultipliers[key].multiplier = value;
            }
        }
        alert('تم تحديث معاملات التسليم بنجاح');
    };
    
    // تحديث التكاليف الإضافية
    updateAdditionalButton.onClick = function() {
        var revision = parseFloat(revisionInput.text);
        var consultation = parseFloat(consultationInput.text);
        var sourceFiles = parseFloat(sourceFilesInput.text);
        
        if (!isNaN(revision) && revision >= 0) {
            PricingConfig.revisionCost = revision;
        }
        if (!isNaN(consultation) && consultation >= 0) {
            PricingConfig.additionalCosts.consultationHour = consultation;
        }
        if (!isNaN(sourceFiles) && sourceFiles >= 0) {
            PricingConfig.additionalCosts.sourceFilesFee = sourceFiles;
        }
        
        alert('تم تحديث التكاليف الإضافية بنجاح');
    };
    
    // حفظ الإعدادات
    saveConfigButton.onClick = function() {
        alert('ميزة حفظ الإعدادات ستكون متاحة في الإصدار القادم');
    };
    
    // تحميل الإعدادات
    loadConfigButton.onClick = function() {
        alert('ميزة تحميل الإعدادات ستكون متاحة في الإصدار القادم');
    };
    
    // إعادة تعيين الإعدادات
    resetConfigButton.onClick = function() {
        var result = confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات للقيم الافتراضية؟');
        if (result) {
            // يمكن إضافة كود إعادة التعيين هنا
            alert('تم إعادة تعيين الإعدادات');
        }
    };
    
    // إغلاق النافذة
    closeButton.onClick = function() {
        dialog.close();
    };
    
    return dialog;
}

// دالة تشغيل مخصص الأسعار
function runPriceCustomizer() {
    var dialog = createCustomizerUI();
    dialog.show();
}

// دالة عرض نافذة تخصيص الأسعار المتقدمة
function showAdvancedPriceCustomizer() {
    var dialog = new Window('dialog', 'تخصيص الأسعار المتقدم - Advanced Price Customization');
    dialog.orientation = 'column';
    dialog.alignChildren = 'fill';
    dialog.spacing = 15;
    dialog.margins = 20;
    dialog.preferredSize.width = 600;
    dialog.preferredSize.height = 600;

    var title = dialog.add('statictext', undefined, '⚙️ إعدادات التسعير الاحترافية');
    title.graphics.font = ScriptUI.newFont(title.graphics.font.name, ScriptUI.FontStyle.BOLD, 16);

    // مجموعة مستوى الخبرة
    var expGroup = dialog.add('panel', undefined, '🎓 مستوى الخبرة وسعر الساعة');
    expGroup.orientation = 'column';
    expGroup.alignChildren = 'fill';
    expGroup.margins = 15;

    var experienceRadios = {};
    for (var key in CustomizationConfig.experienceLevels) {
        var level = CustomizationConfig.experienceLevels[key];
        var radio = expGroup.add('radiobutton', undefined,
            level.name + ' (' + level.nameEn + ') - $' + level.minRate + '-' + level.maxRate + '/ساعة');
        experienceRadios[key] = radio;

        if (key === CustomizationConfig.userSettings.experienceLevel) {
            radio.value = true;
        }
    }

    // سعر الساعة المخصص
    var rateGroup = expGroup.add('group');
    rateGroup.add('statictext', undefined, 'سعر الساعة المخصص:').preferredSize.width = 150;
    var hourlyRateInput = rateGroup.add('edittext', undefined, CustomizationConfig.userSettings.baseHourlyRate.toString());
    hourlyRateInput.preferredSize.width = 100;
    rateGroup.add('statictext', undefined, 'دولار/ساعة');

    var rateSlider = expGroup.add('slider', undefined, CustomizationConfig.userSettings.baseHourlyRate, 10, 500);
    rateSlider.preferredSize.width = 500;

    // ربط المنزلق بالحقل النصي
    rateSlider.onChanging = function() {
        hourlyRateInput.text = Math.round(this.value).toString();
    };

    hourlyRateInput.onChanging = function() {
        var value = parseFloat(this.text) || 35;
        rateSlider.value = Math.max(10, Math.min(500, value));
    };

    // مجموعة أنواع العملاء
    var clientGroup = dialog.add('panel', undefined, '👥 معاملات أنواع العملاء');
    clientGroup.orientation = 'column';
    clientGroup.alignChildren = 'fill';
    clientGroup.margins = 15;

    var clientInputs = {};
    for (var key in CustomizationConfig.clientTypes) {
        var client = CustomizationConfig.clientTypes[key];
        var row = clientGroup.add('group');
        row.add('statictext', undefined, client.name + ':').preferredSize.width = 150;
        var input = row.add('edittext', undefined, client.multiplier.toString());
        input.preferredSize.width = 80;
        row.add('statictext', undefined, 'x');
        clientInputs[key] = input;
    }

    // مجموعة المناطق الجغرافية
    var regionGroup = dialog.add('panel', undefined, '🌍 معاملات المناطق الجغرافية');
    regionGroup.orientation = 'column';
    regionGroup.alignChildren = 'fill';
    regionGroup.margins = 15;

    var regionInputs = {};
    for (var key in CustomizationConfig.regions) {
        var region = CustomizationConfig.regions[key];
        var row = regionGroup.add('group');
        row.add('statictext', undefined, region.name + ':').preferredSize.width = 150;
        var input = row.add('edittext', undefined, region.multiplier.toString());
        input.preferredSize.width = 80;
        row.add('statictext', undefined, 'x');
        regionInputs[key] = input;
    }

    // أزرار التحكم
    var buttonGroup = dialog.add('group');
    buttonGroup.orientation = 'row';
    buttonGroup.alignChildren = 'center';
    buttonGroup.spacing = 15;

    var saveButton = buttonGroup.add('button', undefined, '💾 حفظ الإعدادات');
    var previewButton = buttonGroup.add('button', undefined, '👁️ معاينة');
    var resetButton = buttonGroup.add('button', undefined, '🔄 إعادة تعيين');
    var cancelButton = buttonGroup.add('button', undefined, '❌ إلغاء');

    var result = { saved: false, settings: {} };

    saveButton.onClick = function() {
        try {
            // حفظ مستوى الخبرة
            for (var key in experienceRadios) {
                if (experienceRadios[key].value) {
                    CustomizationConfig.userSettings.experienceLevel = key;
                    break;
                }
            }

            // حفظ سعر الساعة
            CustomizationConfig.userSettings.baseHourlyRate = parseFloat(hourlyRateInput.text) || 35;

            // تطبيق التغييرات على إعدادات التسعير الأساسية
            var expLevel = CustomizationConfig.experienceLevels[CustomizationConfig.userSettings.experienceLevel];
            var newRate = CustomizationConfig.userSettings.baseHourlyRate * expLevel.multiplier;

            // تحديث أسعار أنواع التصميم
            for (var designKey in PricingConfig.designTypes) {
                PricingConfig.designTypes[designKey].basePrice = Math.round(newRate);
            }

            result.saved = true;
            result.settings = CustomizationConfig.userSettings;

            alert('✅ تم حفظ الإعدادات بنجاح!\n\n' +
                  '🎓 مستوى الخبرة: ' + expLevel.name + '\n' +
                  '💰 سعر الساعة الأساسي: $' + CustomizationConfig.userSettings.baseHourlyRate + '\n' +
                  '🔧 سعر الساعة الفعلي: $' + Math.round(newRate) + '\n\n' +
                  'تم تطبيق الإعدادات على جميع أنواع التصميم.');

            dialog.close();

        } catch (error) {
            alert('❌ خطأ في حفظ الإعدادات: ' + error.message);
        }
    };

    previewButton.onClick = function() {
        var selectedExperience = 'intermediate';
        for (var key in experienceRadios) {
            if (experienceRadios[key].value) {
                selectedExperience = key;
                break;
            }
        }

        var expLevel = CustomizationConfig.experienceLevels[selectedExperience];
        var hourlyRate = parseFloat(hourlyRateInput.text) || 35;
        var effectiveRate = hourlyRate * expLevel.multiplier;

        var preview = '📊 معاينة الإعدادات:\n\n';
        preview += '🎓 مستوى الخبرة: ' + expLevel.name + ' (x' + expLevel.multiplier + ')\n';
        preview += '💰 سعر الساعة الأساسي: $' + hourlyRate + '\n';
        preview += '💵 سعر الساعة الفعلي: $' + Math.round(effectiveRate) + '\n\n';
        preview += '📈 أمثلة لمشاريع مختلفة (5 ساعات):\n';
        preview += '• لوجو بسيط: $' + Math.round(effectiveRate * 5 * 1.0) + '\n';
        preview += '• بروشور متوسط: $' + Math.round(effectiveRate * 5 * 1.5) + '\n';
        preview += '• موقع معقد: $' + Math.round(effectiveRate * 5 * 2.0) + '\n\n';
        preview += '👥 معاملات العملاء:\n';

        for (var key in clientInputs) {
            var client = CustomizationConfig.clientTypes[key];
            var multiplier = parseFloat(clientInputs[key].text) || 1.0;
            preview += '• ' + client.name + ': x' + multiplier + '\n';
        }

        alert(preview);
    };

    resetButton.onClick = function() {
        var confirm = Window.confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات؟');
        if (confirm) {
            // إعادة تعيين القيم الافتراضية
            hourlyRateInput.text = '35';
            rateSlider.value = 35;
            experienceRadios['intermediate'].value = true;

            for (var key in clientInputs) {
                clientInputs[key].text = CustomizationConfig.clientTypes[key].multiplier.toString();
            }

            for (var key in regionInputs) {
                regionInputs[key].text = CustomizationConfig.regions[key].multiplier.toString();
            }
        }
    };

    cancelButton.onClick = function() {
        dialog.close();
    };

    dialog.show();
    return result;
}

// تشغيل المخصص إذا تم استدعاء الملف مباشرة
if (typeof main === 'undefined') {
    runPriceCustomizer();
}
