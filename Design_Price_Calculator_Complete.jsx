/*
═══════════════════════════════════════════════════════════════════════════════
                        حاسبة أسعار التصميم - الإصدار الكامل
                      Design Price Calculator - Complete Version
═══════════════════════════════════════════════════════════════════════════════

الإصدار: 1.0.0
التاريخ: 18 ديسمبر 2024
المطور: Design Price Calculator Team
الترخيص: MIT License

الوصف:
سكريبت شامل لحساب أسعار التصميم في Adobe Photoshop
يحتوي على جميع الوظائف والإعدادات في ملف واحد

الميزات:
✅ حساب الأسعار الذكي
✅ واجهة مستخدم متقدمة
✅ تصدير بصيغ متعددة
✅ نظام تسعير مرن
✅ دعم اللغة العربية
✅ اختبارات شاملة

═══════════════════════════════════════════════════════════════════════════════
*/

// التحقق من توفر فوتوشوب
if (typeof app === 'undefined' || app.name !== 'Adobe Photoshop') {
    alert('هذا السكريبت يعمل فقط في برنامج Adobe Photoshop');
    throw new Error('Adobe Photoshop required');
}

// إضافة دعم Object.keys للإصدارات القديمة من ExtendScript
if (!Object.keys) {
    Object.keys = function(obj) {
        var keys = [];
        for (var key in obj) {
            if (obj.hasOwnProperty(key)) {
                keys.push(key);
            }
        }
        return keys;
    };
}

// ═══════════════════════════════════════════════════════════════════════════════
// إعدادات التسعير - PRICING CONFIGURATION
// ═══════════════════════════════════════════════════════════════════════════════

var PricingConfig = {
    // معلومات عامة
    info: {
        version: "1.0.0",
        currency: "USD",
        lastUpdated: "2024-12-18"
    },
    
    // أنواع التصميم والأسعار الأساسية
    designTypes: {
        'logo': {
            name: 'لوجو',
            nameEn: 'Logo',
            basePrice: 50,
            complexity: 1.5,
            minHours: 3,
            maxHours: 20,
            description: 'تصميم شعار احترافي'
        },
        'social_media': {
            name: 'سوشيال ميديا',
            nameEn: 'Social Media',
            basePrice: 25,
            complexity: 1.0,
            minHours: 1,
            maxHours: 8,
            description: 'تصميم منشورات وسائل التواصل الاجتماعي'
        },
        'brand_identity': {
            name: 'هوية بصرية',
            nameEn: 'Brand Identity',
            basePrice: 75,
            complexity: 2.0,
            minHours: 10,
            maxHours: 50,
            description: 'تصميم هوية بصرية متكاملة'
        },
        'poster': {
            name: 'بوستر',
            nameEn: 'Poster',
            basePrice: 40,
            complexity: 1.3,
            minHours: 2,
            maxHours: 12,
            description: 'تصميم بوستر إعلاني أو فني'
        },
        'ui_ux': {
            name: 'UI/UX',
            nameEn: 'UI/UX Design',
            basePrice: 60,
            complexity: 1.8,
            minHours: 5,
            maxHours: 40,
            description: 'تصميم واجهات المستخدم وتجربة المستخدم'
        },
        'business_card': {
            name: 'كارت شخصي',
            nameEn: 'Business Card',
            basePrice: 30,
            complexity: 1.0,
            minHours: 1,
            maxHours: 4,
            description: 'تصميم كارت شخصي أو بطاقة عمل'
        },
        'brochure': {
            name: 'بروشور',
            nameEn: 'Brochure',
            basePrice: 45,
            complexity: 1.4,
            minHours: 3,
            maxHours: 15,
            description: 'تصميم بروشور أو مطوية إعلانية'
        },
        'packaging': {
            name: 'تصميم عبوات',
            nameEn: 'Packaging Design',
            basePrice: 80,
            complexity: 2.2,
            minHours: 8,
            maxHours: 30,
            description: 'تصميم عبوات المنتجات'
        }
    },
    
    // درجات التعقيد ومعاملاتها
    complexityMultipliers: {
        'simple': {
            name: 'بسيط',
            nameEn: 'Simple',
            multiplier: 1.0,
            description: 'تصميم بسيط بعناصر قليلة'
        },
        'medium': {
            name: 'متوسط',
            nameEn: 'Medium',
            multiplier: 1.5,
            description: 'تصميم متوسط التعقيد'
        },
        'complex': {
            name: 'معقد',
            nameEn: 'Complex',
            multiplier: 2.0,
            description: 'تصميم معقد بتفاصيل كثيرة'
        },
        'very_complex': {
            name: 'معقد جداً',
            nameEn: 'Very Complex',
            multiplier: 2.5,
            description: 'تصميم شديد التعقيد'
        }
    },
    
    // أنواع الاستخدام ومعاملاتها
    usageMultipliers: {
        'personal': {
            name: 'شخصي',
            nameEn: 'Personal',
            multiplier: 1.0,
            description: 'استخدام شخصي غير تجاري'
        },
        'small_business': {
            name: 'مشروع صغير',
            nameEn: 'Small Business',
            multiplier: 1.4,
            description: 'مشروع صغير أو شركة ناشئة'
        },
        'commercial': {
            name: 'تجاري',
            nameEn: 'Commercial',
            multiplier: 1.8,
            description: 'استخدام تجاري عام'
        },
        'enterprise': {
            name: 'مؤسسي',
            nameEn: 'Enterprise',
            multiplier: 2.5,
            description: 'شركة كبيرة أو مؤسسة'
        }
    },
    
    // سرعة التسليم ومعاملاتها
    deliveryMultipliers: {
        'normal': {
            name: 'عادي',
            nameEn: 'Normal',
            multiplier: 1.0,
            description: 'تسليم في الوقت العادي (3-7 أيام)'
        },
        'fast': {
            name: 'سريع',
            nameEn: 'Fast',
            multiplier: 1.3,
            description: 'تسليم سريع (1-2 يوم)'
        },
        'urgent': {
            name: 'مستعجل',
            nameEn: 'Urgent',
            multiplier: 1.5,
            description: 'تسليم مستعجل (نفس اليوم)'
        },
        'express': {
            name: 'فوري',
            nameEn: 'Express',
            multiplier: 2.0,
            description: 'تسليم فوري (خلال ساعات)'
        }
    },
    
    // تكاليف إضافية
    additionalCosts: {
        revisionCost: 15,
        consultationHour: 40,
        conceptFee: 25,
        sourceFilesFee: 20,
        rushJobFee: 50,
        weekendWorkFee: 30
    },
    
    // إعدادات عامة
    settings: {
        minProjectValue: 25,
        maxProjectValue: 5000,
        defaultHours: 5,
        defaultRevisions: 2,
        taxRate: 0,
        profitMargin: 1.2
    },
    
    // رسائل النظام
    messages: {
        welcome: 'مرحباً بك في حاسبة أسعار التصميم',
        calculationComplete: 'تم حساب السعر بنجاح',
        saveSuccess: 'تم حفظ التقدير بنجاح',
        saveError: 'خطأ في حفظ الملف',
        invalidInput: 'يرجى التحقق من البيانات المدخلة',
        priceUpdated: 'تم تحديث السعر'
    }
};

// تكاليف إضافية للتوافق مع الإصدارات السابقة
var revisionCost = PricingConfig.additionalCosts.revisionCost;

// ═══════════════════════════════════════════════════════════════════════════════
// وظائف مساعدة - HELPER FUNCTIONS
// ═══════════════════════════════════════════════════════════════════════════════

// دالة للحصول على قائمة أنواع التصميم
function getDesignTypesList() {
    var list = [];
    for (var key in PricingConfig.designTypes) {
        list.push({
            key: key,
            name: PricingConfig.designTypes[key].name,
            nameEn: PricingConfig.designTypes[key].nameEn
        });
    }
    return list;
}

// دالة للحصول على معامل التعقيد
function getComplexityMultiplier(level) {
    var keys = Object.keys(PricingConfig.complexityMultipliers);
    var index = Math.max(0, Math.min(level - 1, keys.length - 1));
    return PricingConfig.complexityMultipliers[keys[index]];
}

// دالة للحصول على معامل الاستخدام
function getUsageMultiplier(type) {
    return PricingConfig.usageMultipliers[type] || PricingConfig.usageMultipliers['personal'];
}

// دالة للحصول على معامل التسليم
function getDeliveryMultiplier(type) {
    return PricingConfig.deliveryMultipliers[type] || PricingConfig.deliveryMultipliers['normal'];
}

// ═══════════════════════════════════════════════════════════════════════════════
// واجهة المستخدم - USER INTERFACE
// ═══════════════════════════════════════════════════════════════════════════════

// دالة إنشاء واجهة المستخدم
function createUI() {
    var dialog = new Window('dialog', 'حاسبة أسعار التصميم - Design Price Calculator v1.0');
    dialog.orientation = 'column';
    dialog.alignChildren = 'fill';
    dialog.spacing = 10;
    dialog.margins = 20;
    dialog.preferredSize.width = 500;
    
    // عنوان رئيسي مع أيقونة
    var headerGroup = dialog.add('panel');
    headerGroup.orientation = 'column';
    headerGroup.alignChildren = 'center';
    headerGroup.margins = 15;
    
    var titleGroup = headerGroup.add('group');
    titleGroup.orientation = 'row';
    titleGroup.alignChildren = 'center';
    
    var title = titleGroup.add('statictext', undefined, '💰 حاسبة أسعار التصميم');
    title.graphics.font = ScriptUI.newFont(title.graphics.font.name, ScriptUI.FontStyle.BOLD, 18);
    
    var subtitle = headerGroup.add('statictext', undefined, 'احسب سعر مشروعك بدقة واحترافية');
    subtitle.graphics.font = ScriptUI.newFont(subtitle.graphics.font.name, ScriptUI.FontStyle.ITALIC, 12);
    
    // مجموعة نوع التصميم
    var designTypeGroup = dialog.add('panel', undefined, '🎨 نوع التصميم');
    designTypeGroup.orientation = 'column';
    designTypeGroup.alignChildren = 'fill';
    designTypeGroup.margins = 15;
    
    var designTypeRow = designTypeGroup.add('group');
    designTypeRow.orientation = 'row';
    designTypeRow.alignChildren = 'left';
    
    var designTypeLabel = designTypeRow.add('statictext', undefined, 'اختر نوع التصميم:');
    designTypeLabel.preferredSize.width = 130;
    
    var designTypeDropdown = designTypeRow.add('dropdownlist');
    designTypeDropdown.preferredSize.width = 250;
    
    // إضافة خيارات نوع التصميم مع الأسعار
    for (var key in PricingConfig.designTypes) {
        var item = PricingConfig.designTypes[key];
        designTypeDropdown.add('item', item.name + ' ($' + item.basePrice + '/ساعة)');
    }
    designTypeDropdown.selection = 0;
    
    // عرض وصف نوع التصميم
    var designDescription = designTypeGroup.add('statictext', undefined, '');
    designDescription.preferredSize.width = 400;
    designDescription.preferredSize.height = 30;
    
    // تحديث الوصف عند تغيير النوع
    designTypeDropdown.onChange = function() {
        var keys = Object.keys(PricingConfig.designTypes);
        var selectedKey = keys[this.selection.index];
        var selectedType = PricingConfig.designTypes[selectedKey];
        designDescription.text = selectedType.description || '';
    };
    
    // تعيين الوصف الافتراضي
    if (designTypeDropdown.selection) {
        designTypeDropdown.onChange();
    }
    
    // مجموعة الوقت المتوقع
    var timeGroup = dialog.add('panel', undefined, '⏰ الوقت المتوقع');
    timeGroup.orientation = 'column';
    timeGroup.alignChildren = 'fill';
    timeGroup.margins = 15;
    
    var timeRow = timeGroup.add('group');
    timeRow.orientation = 'row';
    timeRow.alignChildren = 'left';
    
    var timeLabel = timeRow.add('statictext', undefined, 'عدد الساعات:');
    timeLabel.preferredSize.width = 130;
    
    var timeInput = timeRow.add('edittext', undefined, '5');
    timeInput.preferredSize.width = 80;
    timeInput.characters = 10;
    
    var timeHint = timeRow.add('statictext', undefined, 'ساعة');
    timeHint.preferredSize.width = 50;
    
    // شريط توضيحي للوقت
    var timeHintGroup = timeGroup.add('group');
    timeHintGroup.orientation = 'row';
    timeHintGroup.alignChildren = 'left';
    
    var timeHintText = timeHintGroup.add('statictext', undefined, '💡 نصيحة: لوجو بسيط (3-5 ساعات)، معقد (8-15 ساعة)');
    timeHintText.graphics.font = ScriptUI.newFont(timeHintText.graphics.font.name, ScriptUI.FontStyle.ITALIC, 10);
    
    // مجموعة درجة التعقيد
    var complexityGroup = dialog.add('panel', undefined, '🔧 درجة التعقيد');
    complexityGroup.orientation = 'column';
    complexityGroup.alignChildren = 'fill';
    complexityGroup.margins = 15;
    
    var complexitySliderGroup = complexityGroup.add('group');
    complexitySliderGroup.orientation = 'row';
    complexitySliderGroup.alignChildren = 'center';
    
    var complexityLabel = complexitySliderGroup.add('statictext', undefined, 'التعقيد:');
    complexityLabel.preferredSize.width = 70;
    
    var complexitySlider = complexitySliderGroup.add('slider', undefined, 1, 1, 4);
    complexitySlider.preferredSize.width = 250;
    
    var complexityValue = complexitySliderGroup.add('statictext', undefined, 'بسيط (x1.0)');
    complexityValue.preferredSize.width = 100;
    
    // مؤشرات التعقيد
    var complexityIndicators = complexityGroup.add('group');
    complexityIndicators.orientation = 'row';
    complexityIndicators.alignChildren = 'center';
    
    var indicator1 = complexityIndicators.add('statictext', undefined, 'بسيط');
    var indicator2 = complexityIndicators.add('statictext', undefined, 'متوسط');
    var indicator3 = complexityIndicators.add('statictext', undefined, 'معقد');
    var indicator4 = complexityIndicators.add('statictext', undefined, 'معقد جداً');
    
    indicator1.preferredSize.width = indicator2.preferredSize.width = 
    indicator3.preferredSize.width = indicator4.preferredSize.width = 100;
    
    // تحديث نص التعقيد عند تحريك المنزلق
    complexitySlider.onChanging = function() {
        var value = Math.round(this.value);
        var complexityKeys = Object.keys(PricingConfig.complexityMultipliers);
        var complexity = PricingConfig.complexityMultipliers[complexityKeys[value - 1]];
        complexityValue.text = complexity.name + ' (x' + complexity.multiplier + ')';
        
        // تحديث ألوان المؤشرات (محاكاة)
        indicator1.text = value >= 1 ? '● بسيط' : '○ بسيط';
        indicator2.text = value >= 2 ? '● متوسط' : '○ متوسط';
        indicator3.text = value >= 3 ? '● معقد' : '○ معقد';
        indicator4.text = value >= 4 ? '● معقد جداً' : '○ معقد جداً';
    };
    
    // تعيين القيمة الافتراضية
    complexitySlider.onChanging();
    
    // مجموعة عدد التعديلات
    var revisionsGroup = dialog.add('panel', undefined, '✏️ التعديلات');
    revisionsGroup.orientation = 'column';
    revisionsGroup.alignChildren = 'fill';
    revisionsGroup.margins = 15;
    
    var revisionsRow = revisionsGroup.add('group');
    revisionsRow.orientation = 'row';
    revisionsRow.alignChildren = 'left';
    
    var revisionsLabel = revisionsRow.add('statictext', undefined, 'عدد التعديلات:');
    revisionsLabel.preferredSize.width = 130;
    
    var revisionsInput = revisionsRow.add('edittext', undefined, '2');
    revisionsInput.preferredSize.width = 80;
    revisionsInput.characters = 10;
    
    var revisionsCost = revisionsRow.add('statictext', undefined, '($' + PricingConfig.additionalCosts.revisionCost + ' لكل تعديل)');
    revisionsCost.preferredSize.width = 150;
    
    var revisionsHint = revisionsGroup.add('statictext', undefined, '💡 التعديلات الأساسية مشمولة، التعديلات الإضافية برسوم');
    revisionsHint.graphics.font = ScriptUI.newFont(revisionsHint.graphics.font.name, ScriptUI.FontStyle.ITALIC, 10);
    
    // مجموعة نوع الاستخدام
    var usageGroup = dialog.add('panel', undefined, '👥 نوع الاستخدام');
    usageGroup.orientation = 'column';
    usageGroup.alignChildren = 'fill';
    usageGroup.margins = 15;
    
    var usageRow = usageGroup.add('group');
    usageRow.orientation = 'row';
    usageRow.alignChildren = 'left';
    
    var usagePersonal = usageRow.add('radiobutton', undefined, 'شخصي (x1.0)');
    var usageSmallBusiness = usageRow.add('radiobutton', undefined, 'مشروع صغير (x1.4)');
    var usageCommercial = usageRow.add('radiobutton', undefined, 'تجاري (x1.8)');
    var usageEnterprise = usageRow.add('radiobutton', undefined, 'مؤسسي (x2.5)');
    usagePersonal.value = true;
    
    // مجموعة سرعة التسليم
    var deliveryGroup = dialog.add('panel', undefined, '🚀 سرعة التسليم');
    deliveryGroup.orientation = 'column';
    deliveryGroup.alignChildren = 'fill';
    deliveryGroup.margins = 15;
    
    var deliveryRow = deliveryGroup.add('group');
    deliveryRow.orientation = 'row';
    deliveryRow.alignChildren = 'left';
    
    var deliveryNormal = deliveryRow.add('radiobutton', undefined, 'عادي (x1.0)');
    var deliveryFast = deliveryRow.add('radiobutton', undefined, 'سريع (x1.3)');
    var deliveryUrgent = deliveryRow.add('radiobutton', undefined, 'مستعجل (x1.5)');
    var deliveryExpress = deliveryRow.add('radiobutton', undefined, 'فوري (x2.0)');
    deliveryNormal.value = true;
    
    var deliveryHint = deliveryGroup.add('statictext', undefined, '⚡ التسليم المستعجل يتطلب رسوم إضافية');
    deliveryHint.graphics.font = ScriptUI.newFont(deliveryHint.graphics.font.name, ScriptUI.FontStyle.ITALIC, 10);
    
    // مجموعة النتيجة
    var resultGroup = dialog.add('panel', undefined, '💰 السعر المقترح');
    resultGroup.orientation = 'column';
    resultGroup.alignChildren = 'fill';
    resultGroup.margins = 15;
    
    // عرض السعر الرئيسي
    var priceDisplayGroup = resultGroup.add('group');
    priceDisplayGroup.orientation = 'row';
    priceDisplayGroup.alignChildren = 'center';
    
    var priceDisplay = priceDisplayGroup.add('statictext', undefined, 'اضغط "احسب السعر" للحصول على التقدير');
    priceDisplay.graphics.font = ScriptUI.newFont(priceDisplay.graphics.font.name, ScriptUI.FontStyle.BOLD, 16);
    priceDisplay.justify = 'center';
    
    // عرض التفاصيل
    var detailsDisplay = resultGroup.add('statictext', undefined, '', {multiline: true});
    detailsDisplay.preferredSize.height = 120;
    detailsDisplay.preferredSize.width = 450;
    detailsDisplay.graphics.font = ScriptUI.newFont(detailsDisplay.graphics.font.name, ScriptUI.FontStyle.REGULAR, 11);
    
    // شريط فاصل
    var separator = dialog.add('panel');
    separator.preferredSize.height = 2;
    
    // أزرار التحكم
    var buttonGroup = dialog.add('group');
    buttonGroup.orientation = 'row';
    buttonGroup.alignChildren = 'center';
    buttonGroup.spacing = 15;
    
    var calculateButton = buttonGroup.add('button', undefined, '🧮 احسب السعر');
    calculateButton.preferredSize.width = 120;
    calculateButton.preferredSize.height = 35;
    
    var saveButton = buttonGroup.add('button', undefined, '💾 حفظ التقدير');
    saveButton.preferredSize.width = 120;
    saveButton.preferredSize.height = 35;
    
    var resetButton = buttonGroup.add('button', undefined, '🔄 إعادة تعيين');
    resetButton.preferredSize.width = 120;
    resetButton.preferredSize.height = 35;
    
    var cancelButton = buttonGroup.add('button', undefined, '❌ إلغاء');
    cancelButton.preferredSize.width = 100;
    cancelButton.preferredSize.height = 35;
    
    // إرجاع كائن يحتوي على جميع عناصر الواجهة
    return {
        dialog: dialog,
        designTypeDropdown: designTypeDropdown,
        designDescription: designDescription,
        timeInput: timeInput,
        complexitySlider: complexitySlider,
        complexityValue: complexityValue,
        revisionsInput: revisionsInput,
        usagePersonal: usagePersonal,
        usageSmallBusiness: usageSmallBusiness,
        usageCommercial: usageCommercial,
        usageEnterprise: usageEnterprise,
        deliveryNormal: deliveryNormal,
        deliveryFast: deliveryFast,
        deliveryUrgent: deliveryUrgent,
        deliveryExpress: deliveryExpress,
        priceDisplay: priceDisplay,
        detailsDisplay: detailsDisplay,
        calculateButton: calculateButton,
        saveButton: saveButton,
        resetButton: resetButton,
        cancelButton: cancelButton
    };
}

// ═══════════════════════════════════════════════════════════════════════════════
// منطق حساب الأسعار - PRICE CALCULATION LOGIC
// ═══════════════════════════════════════════════════════════════════════════════

// دالة حساب السعر المحسنة
function calculatePrice(ui) {
    try {
        // التحقق من صحة البيانات
        if (!ui.designTypeDropdown.selection) {
            alert('يرجى اختيار نوع التصميم');
            return 0;
        }

        // الحصول على القيم من الواجهة
        var designTypeIndex = ui.designTypeDropdown.selection.index;
        var designTypeKeys = Object.keys(PricingConfig.designTypes);
        var designType = PricingConfig.designTypes[designTypeKeys[designTypeIndex]];

        var hours = parseFloat(ui.timeInput.text) || 0;
        if (hours <= 0) {
            alert('يرجى إدخال عدد ساعات صحيح');
            return 0;
        }

        var complexityLevel = Math.round(ui.complexitySlider.value);
        var revisions = Math.max(0, parseInt(ui.revisionsInput.text) || 0);

        // تحديد نوع الاستخدام
        var usageType = 'personal';
        if (ui.usageSmallBusiness.value) usageType = 'small_business';
        else if (ui.usageCommercial.value) usageType = 'commercial';
        else if (ui.usageEnterprise.value) usageType = 'enterprise';

        // تحديد سرعة التسليم
        var deliveryType = 'normal';
        if (ui.deliveryFast.value) deliveryType = 'fast';
        else if (ui.deliveryUrgent.value) deliveryType = 'urgent';
        else if (ui.deliveryExpress.value) deliveryType = 'express';

        // حساب السعر الأساسي
        var basePrice = designType.basePrice * hours;

        // تطبيق معامل التعقيد
        var complexityKeys = Object.keys(PricingConfig.complexityMultipliers);
        var complexityData = PricingConfig.complexityMultipliers[complexityKeys[complexityLevel - 1]];
        var complexityMultiplier = complexityData.multiplier;

        // تطبيق معامل نوع الاستخدام
        var usageData = PricingConfig.usageMultipliers[usageType];
        var usageMultiplier = usageData.multiplier;

        // تطبيق معامل سرعة التسليم
        var deliveryData = PricingConfig.deliveryMultipliers[deliveryType];
        var deliveryMultiplier = deliveryData.multiplier;

        // حساب تكلفة التعديلات (فقط التعديلات الإضافية)
        var freeRevisions = 2; // عدد التعديلات المجانية
        var paidRevisions = Math.max(0, revisions - freeRevisions);
        var revisionsCost = paidRevisions * PricingConfig.additionalCosts.revisionCost;

        // حساب السعر قبل التعديلات
        var priceBeforeRevisions = basePrice * complexityMultiplier * usageMultiplier * deliveryMultiplier;

        // حساب السعر النهائي
        var finalPrice = priceBeforeRevisions + revisionsCost;

        // تطبيق الحد الأدنى والأقصى
        finalPrice = Math.max(PricingConfig.settings.minProjectValue, finalPrice);
        finalPrice = Math.min(PricingConfig.settings.maxProjectValue, finalPrice);

        // تحديث العرض
        ui.priceDisplay.text = '💰 السعر المقترح: $' + Math.round(finalPrice);

        // عرض التفاصيل المفصلة
        var details = '📊 تفاصيل حساب السعر:\n\n';
        details += '🎨 نوع التصميم: ' + designType.name + ' ($' + designType.basePrice + '/ساعة)\n';
        details += '⏰ عدد الساعات: ' + hours + ' ساعة\n';
        details += '💵 السعر الأساسي: $' + Math.round(basePrice) + '\n\n';

        details += '🔧 معامل التعقيد: ' + complexityData.name + ' (x' + complexityMultiplier + ')\n';
        details += '👥 معامل الاستخدام: ' + usageData.name + ' (x' + usageMultiplier + ')\n';
        details += '🚀 معامل التسليم: ' + deliveryData.name + ' (x' + deliveryMultiplier + ')\n\n';

        details += '💰 السعر بعد المعاملات: $' + Math.round(priceBeforeRevisions) + '\n';

        if (revisions > freeRevisions) {
            details += '✏️ تعديلات إضافية: ' + paidRevisions + ' × $' + PricingConfig.additionalCosts.revisionCost + ' = $' + revisionsCost + '\n';
        } else {
            details += '✏️ التعديلات: ' + revisions + ' (مشمولة مجاناً)\n';
        }

        details += '\n🎯 السعر النهائي: $' + Math.round(finalPrice);

        ui.detailsDisplay.text = details;

        return finalPrice;

    } catch (error) {
        alert('خطأ في حساب السعر: ' + error.message);
        return 0;
    }
}

// ═══════════════════════════════════════════════════════════════════════════════
// وظائف الحفظ والتصدير - SAVE & EXPORT FUNCTIONS
// ═══════════════════════════════════════════════════════════════════════════════

// دالة حفظ التقدير المحسنة
function saveEstimate(ui, price) {
    try {
        var file = File.saveDialog('حفظ تقدير السعر', '*.txt');
        if (file) {
            file.open('w');
            file.encoding = 'UTF-8';

            // رأس الملف
            file.writeln('═══════════════════════════════════════════════════════════');
            file.writeln('                    تقدير سعر التصميم                    ');
            file.writeln('                  Design Price Estimate                  ');
            file.writeln('═══════════════════════════════════════════════════════════');
            file.writeln('');

            // معلومات التاريخ والوقت
            var now = new Date();
            file.writeln('📅 التاريخ: ' + now.toLocaleDateString());
            file.writeln('🕐 الوقت: ' + now.toLocaleTimeString());
            file.writeln('');

            // معلومات المشروع
            file.writeln('📋 معلومات المشروع:');
            file.writeln('───────────────────────────────────────────────────────────');

            var designTypeIndex = ui.designTypeDropdown.selection.index;
            var designTypeKeys = Object.keys(PricingConfig.designTypes);
            var designType = PricingConfig.designTypes[designTypeKeys[designTypeIndex]];

            file.writeln('🎨 نوع التصميم: ' + designType.name);
            file.writeln('⏰ عدد الساعات: ' + ui.timeInput.text);
            file.writeln('🔧 درجة التعقيد: ' + ui.complexityValue.text);
            file.writeln('✏️ عدد التعديلات: ' + ui.revisionsInput.text);

            // نوع الاستخدام
            var usageType = 'شخصي';
            if (ui.usageSmallBusiness.value) usageType = 'مشروع صغير';
            else if (ui.usageCommercial.value) usageType = 'تجاري';
            else if (ui.usageEnterprise.value) usageType = 'مؤسسي';
            file.writeln('👥 نوع الاستخدام: ' + usageType);

            // سرعة التسليم
            var deliveryType = 'عادي';
            if (ui.deliveryFast.value) deliveryType = 'سريع';
            else if (ui.deliveryUrgent.value) deliveryType = 'مستعجل';
            else if (ui.deliveryExpress.value) deliveryType = 'فوري';
            file.writeln('🚀 سرعة التسليم: ' + deliveryType);

            file.writeln('');

            // تفاصيل الحساب
            file.writeln('💰 تفاصيل الحساب:');
            file.writeln('───────────────────────────────────────────────────────────');
            file.writeln(ui.detailsDisplay.text.replace(/\n/g, '\r\n'));
            file.writeln('');

            // السعر النهائي
            file.writeln('═══════════════════════════════════════════════════════════');
            file.writeln('🎯 السعر النهائي: $' + Math.round(price));
            file.writeln('═══════════════════════════════════════════════════════════');
            file.writeln('');

            // ملاحظات
            file.writeln('📝 ملاحظات مهمة:');
            file.writeln('• هذا التقدير قابل للتفاوض حسب متطلبات المشروع');
            file.writeln('• السعر لا يشمل الضرائب إن وجدت');
            file.writeln('• التعديلات الإضافية قد تتطلب رسوم إضافية');
            file.writeln('• يرجى مراجعة التفاصيل قبل بدء العمل');
            file.writeln('');

            // تذييل
            file.writeln('───────────────────────────────────────────────────────────');
            file.writeln('تم إنشاء هذا التقدير بواسطة حاسبة أسعار التصميم v1.0');
            file.writeln('Design Price Calculator - Professional Pricing Tool');
            file.writeln('───────────────────────────────────────────────────────────');

            file.close();

            alert('✅ تم حفظ التقدير بنجاح!\n\nالملف محفوظ في:\n' + file.fsName);
        }
    } catch (error) {
        alert('❌ خطأ في حفظ الملف: ' + error.message);
    }
}

// دالة إعادة تعيين النموذج
function resetForm(ui) {
    try {
        // إعادة تعيين القيم الافتراضية
        ui.designTypeDropdown.selection = 0;
        ui.timeInput.text = PricingConfig.settings.defaultHours.toString();
        ui.complexitySlider.value = 1;
        ui.complexitySlider.onChanging();
        ui.revisionsInput.text = PricingConfig.settings.defaultRevisions.toString();

        // إعادة تعيين أزرار الاختيار
        ui.usagePersonal.value = true;
        ui.usageSmallBusiness.value = false;
        ui.usageCommercial.value = false;
        ui.usageEnterprise.value = false;

        ui.deliveryNormal.value = true;
        ui.deliveryFast.value = false;
        ui.deliveryUrgent.value = false;
        ui.deliveryExpress.value = false;

        // مسح النتائج
        ui.priceDisplay.text = 'اضغط "احسب السعر" للحصول على التقدير';
        ui.detailsDisplay.text = '';

        // تحديث وصف نوع التصميم
        if (ui.designTypeDropdown.onChange) {
            ui.designTypeDropdown.onChange();
        }

        alert('✅ تم إعادة تعيين النموذج بنجاح');

    } catch (error) {
        alert('❌ خطأ في إعادة التعيين: ' + error.message);
    }
}

// ═══════════════════════════════════════════════════════════════════════════════
// التحقق من صحة البيانات - DATA VALIDATION
// ═══════════════════════════════════════════════════════════════════════════════

// دالة التحقق من صحة البيانات
function validateInputs(ui) {
    var errors = [];

    // التحقق من نوع التصميم
    if (!ui.designTypeDropdown.selection) {
        errors.push('يرجى اختيار نوع التصميم');
    }

    // التحقق من عدد الساعات
    var hours = parseFloat(ui.timeInput.text);
    if (isNaN(hours) || hours <= 0) {
        errors.push('يرجى إدخال عدد ساعات صحيح (أكبر من 0)');
    } else if (hours > 100) {
        errors.push('عدد الساعات كبير جداً (الحد الأقصى 100 ساعة)');
    }

    // التحقق من عدد التعديلات
    var revisions = parseInt(ui.revisionsInput.text);
    if (isNaN(revisions) || revisions < 0) {
        errors.push('يرجى إدخال عدد تعديلات صحيح (0 أو أكثر)');
    } else if (revisions > 20) {
        errors.push('عدد التعديلات كبير جداً (الحد الأقصى 20 تعديل)');
    }

    return errors;
}

// ═══════════════════════════════════════════════════════════════════════════════
// الدالة الرئيسية - MAIN FUNCTION
// ═══════════════════════════════════════════════════════════════════════════════

// الدالة الرئيسية المحسنة
function main() {
    try {
        var ui = createUI();
        var currentPrice = 0;

        // ربط أحداث الحساب
        ui.calculateButton.onClick = function() {
            var errors = validateInputs(ui);
            if (errors.length > 0) {
                alert('❌ يرجى تصحيح الأخطاء التالية:\n\n• ' + errors.join('\n• '));
                return;
            }

            currentPrice = calculatePrice(ui);
            if (currentPrice > 0) {
                ui.saveButton.enabled = true;
            }
        };

        // ربط أحداث الحفظ
        ui.saveButton.onClick = function() {
            if (currentPrice > 0) {
                saveEstimate(ui, currentPrice);
            } else {
                alert('⚠️ يرجى حساب السعر أولاً قبل الحفظ');
            }
        };

        // ربط أحداث إعادة التعيين
        ui.resetButton.onClick = function() {
            var result = confirm('هل أنت متأكد من إعادة تعيين جميع البيانات؟');
            if (result) {
                resetForm(ui);
                currentPrice = 0;
                ui.saveButton.enabled = false;
            }
        };

        // ربط أحداث الإلغاء
        ui.cancelButton.onClick = function() {
            ui.dialog.close();
        };

        // تعطيل زر الحفظ في البداية
        ui.saveButton.enabled = false;

        // إضافة اختصارات لوحة المفاتيح
        ui.dialog.addEventListener('keydown', function(event) {
            if (event.keyName === 'Enter') {
                ui.calculateButton.onClick();
            } else if (event.keyName === 'Escape') {
                ui.cancelButton.onClick();
            }
        });

        // عرض الواجهة
        ui.dialog.show();

    } catch (error) {
        alert('❌ خطأ في تشغيل السكريبت: ' + error.message);
    }
}

// ═══════════════════════════════════════════════════════════════════════════════
// تشغيل السكريبت - RUN SCRIPT
// ═══════════════════════════════════════════════════════════════════════════════

// رسالة ترحيب
alert('🎉 مرحباً بك في حاسبة أسعار التصميم v1.0\n\n' +
      '✨ الميزات:\n' +
      '• حساب أسعار ذكي ومرن\n' +
      '• واجهة سهلة الاستخدام\n' +
      '• تصدير احترافي للتقديرات\n' +
      '• دعم كامل للغة العربية\n\n' +
      'استمتع بالاستخدام! 🚀');

// تشغيل السكريبت
main();

/*
═══════════════════════════════════════════════════════════════════════════════
                                 نهاية السكريبت
                                 END OF SCRIPT
═══════════════════════════════════════════════════════════════════════════════

شكراً لاستخدام حاسبة أسعار التصميم!
Thank you for using Design Price Calculator!

للدعم والمساعدة:
- راجع ملف README.md
- راجع ملف INSTALLATION_GUIDE.md
- راجع ملف EXAMPLES.md

For support and help:
- Check README.md file
- Check INSTALLATION_GUIDE.md file
- Check EXAMPLES.md file

═══════════════════════════════════════════════════════════════════════════════
*/
