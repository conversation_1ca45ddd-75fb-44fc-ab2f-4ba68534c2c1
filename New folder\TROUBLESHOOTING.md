# دليل استكشاف الأخطاء - Troubleshooting Guide

## 🚨 المشاكل الشائعة وحلولها

### 1. خطأ "Object.keys is not a function"

**السبب:** هذا خطأ شائع في الإصدارات القديمة من Adobe Photoshop (خاصة CS6 وما قبلها) لأن ExtendScript لا يدعم `Object.keys()` بشكل افتراضي.

**الحلول:**

#### الحل الأول (الأسرع):
```
1. استخدم ملف Design_Price_Calculator_Complete.jsx
2. هذا الملف يحتوي على جميع الإصلاحات المطلوبة
```

#### الحل الثاني:
```
1. شغل ملف compatibility_fix.jsx أولاً
2. ثم شغل Design_Price_Calculator.jsx
```

#### الحل الثالث (للمطورين):
```javascript
// أضف هذا الكود في بداية أي ملف jsx
if (!Object.keys) {
    Object.keys = function(obj) {
        var keys = [];
        for (var key in obj) {
            if (obj.hasOwnProperty(key)) {
                keys.push(key);
            }
        }
        return keys;
    };
}
```

---

### 2. السكريبت لا يظهر في قائمة Scripts

**الأسباب المحتملة:**
- الملف في مكان خاطئ
- امتداد الملف غير صحيح
- مشكلة في أذونات الملف

**الحلول:**

#### تحقق من المسار:
**Windows:**
```
C:\Program Files\Adobe\Adobe Photoshop [Version]\Presets\Scripts\
```

**macOS:**
```
/Applications/Adobe Photoshop [Version]/Presets/Scripts/
```

#### تحقق من الامتداد:
- يجب أن يكون `.jsx` وليس `.js` أو `.txt`
- تأكد من عدم وجود امتداد مخفي إضافي

#### إعادة تشغيل فوتوشوب:
```
1. أغلق فوتوشوب تماماً
2. أعد تشغيله
3. تحقق من القائمة مرة أخرى
```

---

### 3. خطأ "Cannot read property" أو "undefined"

**السبب:** مشكلة في تحميل الإعدادات أو الملفات المساعدة.

**الحلول:**

#### استخدم الملف الكامل:
```
استخدم Design_Price_Calculator_Complete.jsx بدلاً من الملفات المنفصلة
```

#### تحقق من وجود جميع الملفات:
```
- Design_Price_Calculator.jsx
- pricing_config.jsx
- export_functions.jsx (اختياري)
```

#### تشغيل من Browse:
```
File > Scripts > Browse... > اختر الملف مباشرة
```

---

### 4. الواجهة لا تظهر أو تظهر بشكل خاطئ

**الأسباب المحتملة:**
- دقة الشاشة غير مناسبة
- مشكلة في إعدادات فوتوشوب
- تضارب مع سكريبتات أخرى

**الحلول:**

#### تحقق من دقة الشاشة:
```
- الحد الأدنى: 1024x768
- المُوصى به: 1920x1080 أو أعلى
```

#### إعادة تعيين إعدادات فوتوشوب:
```
1. أغلق فوتوشوب
2. احتفظ بـ Ctrl+Alt+Shift أثناء تشغيل فوتوشوب
3. اختر "Yes" لحذف إعدادات فوتوشوب
```

#### أغلق السكريبتات الأخرى:
```
تأكد من عدم تشغيل سكريبتات أخرى في نفس الوقت
```

---

### 5. خطأ في حفظ الملفات

**الأسباب المحتملة:**
- عدم وجود صلاحيات كتابة
- مساحة القرص ممتلئة
- مسار الحفظ غير صحيح

**الحلول:**

#### تحقق من الصلاحيات:
```
1. شغل فوتوشوب كمدير (Run as Administrator)
2. أو احفظ في مجلد المستندات
```

#### تحقق من المساحة:
```
تأكد من وجود مساحة كافية على القرص (على الأقل 100 MB)
```

#### جرب مسار آخر:
```
احفظ في سطح المكتب أو مجلد المستندات
```

---

### 6. أخطاء في حساب الأسعار

**الأسباب المحتملة:**
- بيانات مدخلة خاطئة
- إعدادات تسعير غير صحيحة
- مشكلة في الحسابات

**الحلول:**

#### تحقق من البيانات:
```
- عدد الساعات: رقم موجب
- عدد التعديلات: رقم صحيح (0 أو أكثر)
- اختيار نوع التصميم
```

#### إعادة تعيين الإعدادات:
```
استخدم زر "إعادة تعيين" في الواجهة
```

#### اختبار بقيم افتراضية:
```
جرب القيم الافتراضية أولاً للتأكد من عمل السكريبت
```

---

## 🔧 أدوات التشخيص

### تشغيل اختبار التوافق:
```
شغل ملف compatibility_fix.jsx للتحقق من التوافق
```

### تشغيل اختبار شامل:
```
شغل ملف test_script.jsx لاختبار جميع الوظائف
```

### فحص سجل الأخطاء:
```
في فوتوشوب: Window > Console (إذا كان متاحاً)
```

---

## 📞 الحصول على المساعدة

### قبل طلب المساعدة، تأكد من:
1. ✅ قراءة هذا الدليل كاملاً
2. ✅ تجربة الحلول المقترحة
3. ✅ تشغيل اختبارات التوافق
4. ✅ التحقق من إصدار فوتوشوب

### معلومات مطلوبة عند طلب المساعدة:
- إصدار Adobe Photoshop
- نظام التشغيل (Windows/Mac + الإصدار)
- نص الخطأ الكامل
- الخطوات التي أدت للخطأ
- الملف المستخدم (Complete أم المنفصل)

---

## 🎯 نصائح لتجنب المشاكل

### 1. استخدم الملف الكامل:
```
Design_Price_Calculator_Complete.jsx هو الأكثر استقراراً
```

### 2. تحديث فوتوشوب:
```
استخدم أحدث إصدار متاح من Adobe Photoshop
```

### 3. نسخ احتياطية:
```
احتفظ بنسخة احتياطية من إعداداتك المخصصة
```

### 4. اختبار دوري:
```
شغل test_script.jsx بشكل دوري للتأكد من سلامة السكريبت
```

---

## 📋 قائمة فحص سريعة

عند مواجهة أي مشكلة، تحقق من:

- [ ] إصدار فوتوشوب يدعم السكريبتات
- [ ] الملف في المجلد الصحيح
- [ ] امتداد الملف `.jsx`
- [ ] إعادة تشغيل فوتوشوب
- [ ] تشغيل compatibility_fix.jsx
- [ ] استخدام Design_Price_Calculator_Complete.jsx
- [ ] صلاحيات الكتابة متاحة
- [ ] مساحة كافية على القرص
- [ ] عدم تشغيل سكريبتات أخرى

---

**ملاحظة:** معظم المشاكل تُحل باستخدام `Design_Price_Calculator_Complete.jsx` بدلاً من الملفات المنفصلة.
