/*
اختبار سكريبت حاسبة أسعار التصميم - Test Script
ملف اختبار للتأكد من عمل جميع وظائف السكريبت بشكل صحيح
*/

// إضافة دعم Object.keys للإصدارات القديمة من ExtendScript
if (!Object.keys) {
    Object.keys = function(obj) {
        var keys = [];
        for (var key in obj) {
            if (obj.hasOwnProperty(key)) {
                keys.push(key);
            }
        }
        return keys;
    };
}

// دالة اختبار الإعدادات الأساسية
function testBasicConfig() {
    var results = [];
    
    try {
        // اختبار وجود كائن الإعدادات
        if (typeof PricingConfig === 'undefined') {
            results.push('❌ كائن PricingConfig غير موجود');
            return results;
        }
        
        results.push('✅ كائن PricingConfig موجود');
        
        // اختبار أنواع التصميم
        if (PricingConfig.designTypes && Object.keys(PricingConfig.designTypes).length > 0) {
            results.push('✅ أنواع التصميم محددة (' + Object.keys(PricingConfig.designTypes).length + ' نوع)');
        } else {
            results.push('❌ أنواع التصميم غير محددة');
        }
        
        // اختبار معاملات التعقيد
        if (PricingConfig.complexityMultipliers && Object.keys(PricingConfig.complexityMultipliers).length > 0) {
            results.push('✅ معاملات التعقيد محددة');
        } else {
            results.push('❌ معاملات التعقيد غير محددة');
        }
        
        // اختبار معاملات الاستخدام
        if (PricingConfig.usageMultipliers && Object.keys(PricingConfig.usageMultipliers).length > 0) {
            results.push('✅ معاملات الاستخدام محددة');
        } else {
            results.push('❌ معاملات الاستخدام غير محددة');
        }
        
        // اختبار معاملات التسليم
        if (PricingConfig.deliveryMultipliers && Object.keys(PricingConfig.deliveryMultipliers).length > 0) {
            results.push('✅ معاملات التسليم محددة');
        } else {
            results.push('❌ معاملات التسليم غير محددة');
        }
        
        // اختبار التكاليف الإضافية
        if (typeof PricingConfig.revisionCost === 'number' && PricingConfig.revisionCost >= 0) {
            results.push('✅ تكلفة التعديلات محددة ($' + PricingConfig.revisionCost + ')');
        } else {
            results.push('❌ تكلفة التعديلات غير محددة');
        }
        
    } catch (error) {
        results.push('❌ خطأ في اختبار الإعدادات: ' + error.message);
    }
    
    return results;
}

// دالة اختبار حساب الأسعار
function testPriceCalculation() {
    var results = [];
    
    try {
        // إنشاء واجهة وهمية للاختبار
        var mockUI = {
            designTypeDropdown: { selection: { index: 0 } },
            timeInput: { text: '5' },
            complexitySlider: { value: 1 },
            revisionsInput: { text: '2' },
            usagePersonal: { value: true },
            usageSmallBusiness: { value: false },
            usageCommercial: { value: false },
            usageEnterprise: { value: false },
            deliveryNormal: { value: true },
            deliveryFast: { value: false },
            deliveryUrgent: { value: false },
            deliveryExpress: { value: false },
            priceDisplay: { text: '' },
            detailsDisplay: { text: '' },
            complexityValue: { text: 'بسيط (x1.0)' }
        };
        
        // اختبار دالة حساب السعر
        if (typeof calculatePrice === 'function') {
            var price = calculatePrice(mockUI);
            if (typeof price === 'number' && price > 0) {
                results.push('✅ دالة حساب السعر تعمل بشكل صحيح (السعر: $' + Math.round(price) + ')');
            } else {
                results.push('❌ دالة حساب السعر لا تعطي نتيجة صحيحة');
            }
        } else {
            results.push('❌ دالة calculatePrice غير موجودة');
        }
        
        // اختبار حالات مختلفة
        mockUI.timeInput.text = '10';
        mockUI.complexitySlider.value = 2;
        mockUI.usageCommercial.value = true;
        mockUI.usagePersonal.value = false;
        
        var price2 = calculatePrice(mockUI);
        if (price2 > price) {
            results.push('✅ المعاملات تؤثر على السعر بشكل صحيح');
        } else {
            results.push('⚠️ قد تكون هناك مشكلة في تطبيق المعاملات');
        }
        
    } catch (error) {
        results.push('❌ خطأ في اختبار حساب الأسعار: ' + error.message);
    }
    
    return results;
}

// دالة اختبار وظائف التصدير
function testExportFunctions() {
    var results = [];
    
    try {
        // اختبار وجود وظائف التصدير
        if (typeof showExportOptions === 'function') {
            results.push('✅ وظائف التصدير المتقدمة متاحة');
        } else {
            results.push('⚠️ وظائف التصدير المتقدمة غير متاحة (ستستخدم الحفظ الأساسي)');
        }
        
        if (typeof saveEstimate === 'function') {
            results.push('✅ دالة الحفظ الأساسية متاحة');
        } else {
            results.push('❌ دالة الحفظ الأساسية غير متاحة');
        }
        
        if (typeof generateDetailedReport === 'function') {
            results.push('✅ دالة إنشاء التقارير متاحة');
        } else {
            results.push('⚠️ دالة إنشاء التقارير غير متاحة');
        }
        
    } catch (error) {
        results.push('❌ خطأ في اختبار وظائف التصدير: ' + error.message);
    }
    
    return results;
}

// دالة اختبار واجهة المستخدم
function testUserInterface() {
    var results = [];
    
    try {
        // اختبار إنشاء الواجهة
        if (typeof createUI === 'function') {
            results.push('✅ دالة إنشاء الواجهة متاحة');
            
            // محاولة إنشاء الواجهة
            var ui = createUI();
            if (ui && ui.dialog) {
                results.push('✅ الواجهة تم إنشاؤها بنجاح');
                
                // اختبار العناصر الأساسية
                var requiredElements = [
                    'designTypeDropdown', 'timeInput', 'complexitySlider',
                    'revisionsInput', 'priceDisplay', 'detailsDisplay',
                    'calculateButton', 'saveButton', 'cancelButton'
                ];
                
                var missingElements = [];
                for (var i = 0; i < requiredElements.length; i++) {
                    if (!ui[requiredElements[i]]) {
                        missingElements.push(requiredElements[i]);
                    }
                }
                
                if (missingElements.length === 0) {
                    results.push('✅ جميع عناصر الواجهة موجودة');
                } else {
                    results.push('❌ عناصر مفقودة: ' + missingElements.join(', '));
                }
                
                // إغلاق الواجهة
                ui.dialog.close();
                
            } else {
                results.push('❌ فشل في إنشاء الواجهة');
            }
        } else {
            results.push('❌ دالة createUI غير موجودة');
        }
        
    } catch (error) {
        results.push('❌ خطأ في اختبار الواجهة: ' + error.message);
    }
    
    return results;
}

// دالة اختبار التحقق من صحة البيانات
function testValidation() {
    var results = [];
    
    try {
        if (typeof validateInputs === 'function') {
            results.push('✅ دالة التحقق من صحة البيانات متاحة');
            
            // اختبار بيانات صحيحة
            var validUI = {
                designTypeDropdown: { selection: { index: 0 } },
                timeInput: { text: '5' },
                revisionsInput: { text: '2' }
            };
            
            var errors = validateInputs(validUI);
            if (errors.length === 0) {
                results.push('✅ التحقق من البيانات الصحيحة يعمل');
            } else {
                results.push('⚠️ مشكلة في التحقق من البيانات الصحيحة');
            }
            
            // اختبار بيانات خاطئة
            var invalidUI = {
                designTypeDropdown: { selection: null },
                timeInput: { text: 'abc' },
                revisionsInput: { text: '-1' }
            };
            
            var errors2 = validateInputs(invalidUI);
            if (errors2.length > 0) {
                results.push('✅ التحقق من البيانات الخاطئة يعمل');
            } else {
                results.push('❌ التحقق من البيانات الخاطئة لا يعمل');
            }
            
        } else {
            results.push('⚠️ دالة التحقق من صحة البيانات غير متاحة');
        }
        
    } catch (error) {
        results.push('❌ خطأ في اختبار التحقق: ' + error.message);
    }
    
    return results;
}

// دالة تشغيل جميع الاختبارات
function runAllTests() {
    var allResults = [];
    
    allResults.push('🧪 بدء اختبار سكريبت حاسبة أسعار التصميم');
    allResults.push('═══════════════════════════════════════════════');
    allResults.push('');
    
    // اختبار الإعدادات الأساسية
    allResults.push('📋 اختبار الإعدادات الأساسية:');
    allResults.push('─────────────────────────────────────────────');
    var configResults = testBasicConfig();
    allResults = allResults.concat(configResults);
    allResults.push('');
    
    // اختبار حساب الأسعار
    allResults.push('💰 اختبار حساب الأسعار:');
    allResults.push('─────────────────────────────────────────────');
    var calcResults = testPriceCalculation();
    allResults = allResults.concat(calcResults);
    allResults.push('');
    
    // اختبار واجهة المستخدم
    allResults.push('🖥️ اختبار واجهة المستخدم:');
    allResults.push('─────────────────────────────────────────────');
    var uiResults = testUserInterface();
    allResults = allResults.concat(uiResults);
    allResults.push('');
    
    // اختبار وظائف التصدير
    allResults.push('📁 اختبار وظائف التصدير:');
    allResults.push('─────────────────────────────────────────────');
    var exportResults = testExportFunctions();
    allResults = allResults.concat(exportResults);
    allResults.push('');
    
    // اختبار التحقق من صحة البيانات
    allResults.push('✔️ اختبار التحقق من صحة البيانات:');
    allResults.push('─────────────────────────────────────────────');
    var validationResults = testValidation();
    allResults = allResults.concat(validationResults);
    allResults.push('');
    
    // إحصائيات النتائج
    var totalTests = 0;
    var passedTests = 0;
    var failedTests = 0;
    var warningTests = 0;
    
    for (var i = 0; i < allResults.length; i++) {
        if (allResults[i].indexOf('✅') === 0) {
            passedTests++;
            totalTests++;
        } else if (allResults[i].indexOf('❌') === 0) {
            failedTests++;
            totalTests++;
        } else if (allResults[i].indexOf('⚠️') === 0) {
            warningTests++;
            totalTests++;
        }
    }
    
    allResults.push('📊 ملخص النتائج:');
    allResults.push('═══════════════════════════════════════════════');
    allResults.push('✅ اختبارات نجحت: ' + passedTests);
    allResults.push('❌ اختبارات فشلت: ' + failedTests);
    allResults.push('⚠️ تحذيرات: ' + warningTests);
    allResults.push('📈 المجموع: ' + totalTests);
    
    if (failedTests === 0) {
        allResults.push('');
        allResults.push('🎉 جميع الاختبارات الأساسية نجحت! السكريبت جاهز للاستخدام.');
    } else {
        allResults.push('');
        allResults.push('⚠️ هناك مشاكل تحتاج إلى إصلاح قبل الاستخدام.');
    }
    
    // عرض النتائج
    var resultDialog = new Window('dialog', 'نتائج اختبار السكريبت');
    resultDialog.orientation = 'column';
    resultDialog.alignChildren = 'fill';
    resultDialog.spacing = 10;
    resultDialog.margins = 20;
    resultDialog.preferredSize.width = 600;
    resultDialog.preferredSize.height = 500;
    
    var resultText = resultDialog.add('edittext', undefined, allResults.join('\n'), {multiline: true, readonly: true});
    resultText.graphics.font = ScriptUI.newFont('Monaco', ScriptUI.FontStyle.REGULAR, 11);
    
    var buttonGroup = resultDialog.add('group');
    buttonGroup.orientation = 'row';
    buttonGroup.alignChildren = 'center';
    
    var saveResultsButton = buttonGroup.add('button', undefined, '💾 حفظ النتائج');
    var closeButton = buttonGroup.add('button', undefined, '❌ إغلاق');
    
    saveResultsButton.onClick = function() {
        try {
            var file = File.saveDialog('حفظ نتائج الاختبار', '*.txt');
            if (file) {
                file.open('w');
                file.encoding = 'UTF-8';
                file.write(allResults.join('\n'));
                file.close();
                alert('تم حفظ نتائج الاختبار بنجاح!');
            }
        } catch (error) {
            alert('خطأ في حفظ النتائج: ' + error.message);
        }
    };
    
    closeButton.onClick = function() {
        resultDialog.close();
    };
    
    resultDialog.show();
}

// تشغيل الاختبارات
runAllTests();
