/*
إصلاحات التوافق - Compatibility Fixes
ملف إصلاحات للمشاكل الشائعة في ExtendScript
*/

// إصلاح Object.keys للإصدارات القديمة
if (!Object.keys) {
    Object.keys = function(obj) {
        var keys = [];
        for (var key in obj) {
            if (obj.hasOwnProperty(key)) {
                keys.push(key);
            }
        }
        return keys;
    };
}

// إصلاح Array.indexOf للإصدارات القديمة
if (!Array.prototype.indexOf) {
    Array.prototype.indexOf = function(searchElement, fromIndex) {
        var k;
        if (this == null) {
            throw new TypeError('"this" is null or not defined');
        }
        var O = Object(this);
        var len = parseInt(O.length) || 0;
        if (len === 0) {
            return -1;
        }
        var n = parseInt(fromIndex) || 0;
        var k;
        if (n >= len) {
            return -1;
        }
        k = n >= 0 ? n : Math.max(len + n, 0);
        for (; k < len; k++) {
            if (k in O && O[k] === searchElement) {
                return k;
            }
        }
        return -1;
    };
}

// إصلاح Array.forEach للإصدارات القديمة
if (!Array.prototype.forEach) {
    Array.prototype.forEach = function(callback, thisArg) {
        var T, k;
        if (this == null) {
            throw new TypeError('this is null or not defined');
        }
        var O = Object(this);
        var len = parseInt(O.length) || 0;
        if (typeof callback !== "function") {
            throw new TypeError(callback + ' is not a function');
        }
        if (arguments.length > 1) {
            T = thisArg;
        }
        k = 0;
        while (k < len) {
            var kValue;
            if (k in O) {
                kValue = O[k];
                callback.call(T, kValue, k, O);
            }
            k++;
        }
    };
}

// إصلاح String.trim للإصدارات القديمة
if (!String.prototype.trim) {
    String.prototype.trim = function() {
        return this.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g, '');
    };
}

// إصلاح JSON.stringify للإصدارات القديمة (مبسط)
if (!JSON || !JSON.stringify) {
    if (!JSON) {
        JSON = {};
    }
    JSON.stringify = function(obj, replacer, space) {
        function stringify(obj, depth) {
            depth = depth || 0;
            var indent = '';
            if (typeof space === 'number') {
                for (var i = 0; i < depth * space; i++) {
                    indent += ' ';
                }
            } else if (typeof space === 'string') {
                for (var i = 0; i < depth; i++) {
                    indent += space;
                }
            }
            
            if (obj === null) return 'null';
            if (typeof obj === 'undefined') return undefined;
            if (typeof obj === 'boolean') return obj.toString();
            if (typeof obj === 'number') return obj.toString();
            if (typeof obj === 'string') return '"' + obj.replace(/"/g, '\\"') + '"';
            
            if (obj instanceof Array) {
                var result = '[\n';
                for (var i = 0; i < obj.length; i++) {
                    if (i > 0) result += ',\n';
                    result += indent + (space ? '  ' : '') + stringify(obj[i], depth + 1);
                }
                result += '\n' + indent + ']';
                return result;
            }
            
            if (typeof obj === 'object') {
                var result = '{\n';
                var first = true;
                for (var key in obj) {
                    if (obj.hasOwnProperty(key)) {
                        if (!first) result += ',\n';
                        result += indent + (space ? '  ' : '') + '"' + key + '": ' + stringify(obj[key], depth + 1);
                        first = false;
                    }
                }
                result += '\n' + indent + '}';
                return result;
            }
            
            return '{}';
        }
        
        return stringify(obj);
    };
}

// دالة للتحقق من التوافق
function checkCompatibility() {
    var issues = [];
    var fixes = [];
    
    // التحقق من Object.keys
    if (typeof Object.keys !== 'function') {
        issues.push('Object.keys غير مدعومة');
    } else {
        fixes.push('Object.keys مدعومة');
    }
    
    // التحقق من Array.indexOf
    if (typeof Array.prototype.indexOf !== 'function') {
        issues.push('Array.indexOf غير مدعومة');
    } else {
        fixes.push('Array.indexOf مدعومة');
    }
    
    // التحقق من JSON.stringify
    if (!JSON || typeof JSON.stringify !== 'function') {
        issues.push('JSON.stringify غير مدعومة');
    } else {
        fixes.push('JSON.stringify مدعومة');
    }
    
    // عرض النتائج
    var message = 'فحص التوافق:\n\n';
    
    if (fixes.length > 0) {
        message += '✅ الوظائف المدعومة:\n';
        for (var i = 0; i < fixes.length; i++) {
            message += '• ' + fixes[i] + '\n';
        }
        message += '\n';
    }
    
    if (issues.length > 0) {
        message += '❌ المشاكل المحتملة:\n';
        for (var i = 0; i < issues.length; i++) {
            message += '• ' + issues[i] + '\n';
        }
        message += '\nتم إضافة إصلاحات للمشاكل المذكورة.';
    } else {
        message += '🎉 جميع الوظائف مدعومة!';
    }
    
    alert(message);
}

// دالة اختبار سريع
function quickTest() {
    try {
        // اختبار Object.keys
        var testObj = {a: 1, b: 2, c: 3};
        var keys = Object.keys(testObj);
        if (keys.length !== 3) {
            throw new Error('Object.keys لا يعمل بشكل صحيح');
        }
        
        // اختبار Array.indexOf
        var testArray = [1, 2, 3, 4, 5];
        if (testArray.indexOf(3) !== 2) {
            throw new Error('Array.indexOf لا يعمل بشكل صحيح');
        }
        
        // اختبار JSON.stringify
        var testJson = JSON.stringify({test: 'value'});
        if (typeof testJson !== 'string') {
            throw new Error('JSON.stringify لا يعمل بشكل صحيح');
        }
        
        alert('✅ جميع الاختبارات نجحت!\nالسكريبت جاهز للاستخدام.');
        return true;
        
    } catch (error) {
        alert('❌ خطأ في الاختبار: ' + error.message);
        return false;
    }
}

// تشغيل فحص التوافق تلقائياً
checkCompatibility();

// عرض خيار الاختبار السريع
var runTest = confirm('هل تريد تشغيل اختبار سريع للتأكد من عمل الإصلاحات؟');
if (runTest) {
    quickTest();
}
