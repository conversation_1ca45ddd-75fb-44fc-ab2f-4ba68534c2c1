# حاسبة أسعار التصميم - Design Price Calculator

سكريبت فوتوشوب لحساب أسعار التصميم بناءً على معايير مختلفة

## الوصف

هذا السكريبت يساعد المصممين في حساب أسعار مشاريع التصميم بطريقة احترافية ومنظمة. يأخذ السكريبت في الاعتبار عدة عوامل مهمة لتحديد السعر المناسب.

## المميزات

### 🎨 أنواع التصميم المدعومة
- لوجو
- سوشيال ميديا
- هوية بصرية
- بوستر
- UI/UX
- كارت شخصي
- بروشور
- تصميم عبوات

### 📊 معايير التسعير
- **الوقت المتوقع**: عدد الساعات المطلوبة للمشروع
- **درجة التعقيد**: بسيط، متوسط، معقد
- **عدد التعديلات**: التعديلات المطلوبة من العميل
- **نوع الاستخدام**: شخصي أو تجاري
- **سرعة التسليم**: عادي أو مستعجل

### 💰 نظام التسعير الذكي
- أسعار أساسية مختلفة لكل نوع تصميم
- معاملات تعقيد قابلة للتخصيص
- رسوم إضافية للاستخدام التجاري
- رسوم إضافية للتسليم المستعجل
- تكلفة محددة لكل تعديل

## طريقة الاستخدام

### التثبيت
1. قم بتحميل ملف `Design_Price_Calculator.jsx`
2. ضع الملف في مجلد Scripts في فوتوشوب:
   - Windows: `C:\Program Files\Adobe\Adobe Photoshop [Version]\Presets\Scripts\`
   - Mac: `/Applications/Adobe Photoshop [Version]/Presets/Scripts/`

### التشغيل
1. افتح Adobe Photoshop
2. اذهب إلى `File > Scripts > Design_Price_Calculator`
3. أو اذهب إلى `File > Scripts > Browse` واختر الملف

### استخدام الواجهة
1. **اختر نوع التصميم** من القائمة المنسدلة
2. **أدخل عدد الساعات** المتوقعة للمشروع
3. **حدد درجة التعقيد** باستخدام المنزلق
4. **أدخل عدد التعديلات** المطلوبة
5. **اختر نوع الاستخدام** (شخصي/تجاري)
6. **اختر سرعة التسليم** (عادي/مستعجل)
7. **اضغط "احسب السعر"** لعرض النتيجة
8. **اضغط "حفظ التقدير"** لحفظ التقدير كملف نصي

## إعدادات التسعير الافتراضية

### الأسعار الأساسية (دولار/ساعة)
- لوجو: $50
- سوشيال ميديا: $25
- هوية بصرية: $75
- بوستر: $40
- UI/UX: $60
- كارت شخصي: $30
- بروشور: $45
- تصميم عبوات: $80

### معاملات التعقيد
- بسيط: x1.0
- متوسط: x1.5
- معقد: x2.0

### معاملات الاستخدام
- شخصي: x1.0
- تجاري: x1.8

### معاملات التسليم
- عادي: x1.0
- مستعجل: x1.5

### تكلفة التعديلات
- $15 لكل تعديل

## التخصيص

يمكنك تخصيص الأسعار والمعاملات عبر تعديل كائن `PricingConfig` في بداية الملف:

```javascript
var PricingConfig = {
    designTypes: {
        'logo': { name: 'لوجو', basePrice: 50, complexity: 1.5 },
        // أضف أو عدل أنواع التصميم هنا
    },
    // عدل المعاملات والأسعار حسب احتياجاتك
};
```

## متطلبات النظام

- Adobe Photoshop CS6 أو أحدث
- نظام التشغيل: Windows أو macOS

## الدعم والمساعدة

إذا واجهت أي مشاكل أو لديك اقتراحات للتحسين، يرجى:
1. التأكد من أن فوتوشوب يدعم تشغيل السكريبتات
2. التأكد من وضع الملف في المجلد الصحيح
3. إعادة تشغيل فوتوشوب بعد إضافة السكريبت

## الترخيص

هذا المشروع مفتوح المصدر ويمكن استخدامه وتعديله بحرية.

## الإصدارات

### الإصدار 1.0
- واجهة مستخدم أساسية
- حساب الأسعار بناءً على المعايير المختلفة
- حفظ التقديرات كملفات نصية
- دعم اللغة العربية

---

**ملاحظة**: هذا السكريبت هو أداة مساعدة لتقدير الأسعار. السعر النهائي يعتمد على عوامل أخرى مثل خبرة المصمم وسوق العمل المحلي.
