/*
═══════════════════════════════════════════════════════════════════════════════
                    حاسبة أسعار التصميم المبسطة v1.0
                    Design Price Calculator Basic v1.0
═══════════════════════════════════════════════════════════════════════════════

الميزات المتاحة:
✅ حساب أسعار أساسي
✅ 5 أنواع تصميم رئيسية
✅ 3 مستويات تعقيد
✅ 3 عملات أساسية (USD, SAR, AED)
✅ تصدير نصي بسيط

تم إزالة:
❌ الواجهة المعقدة
❌ العملات الإضافية
❌ الميزات المتقدمة
❌ التحليل التلقائي
❌ السجل والملاحظات

═══════════════════════════════════════════════════════════════════════════════
*/

// إعدادات التصميم الأساسية
var BasicConfig = {
    // أنواع التصميم الأساسية (5 أنواع فقط)
    designTypes: {
        'logo': { 
            name: 'تصميم شعار / Logo Design', 
            baseHours: 6, 
            basePrice: 150 
        },
        'brochure': { 
            name: 'بروشور/فلاير / Brochure/Flyer', 
            baseHours: 4, 
            basePrice: 100 
        },
        'business_card': { 
            name: 'كارت شخصي / Business Card', 
            baseHours: 2, 
            basePrice: 50 
        },
        'social_media': { 
            name: 'سوشيال ميديا / Social Media', 
            baseHours: 3, 
            basePrice: 75 
        },
        'poster': { 
            name: 'بوستر إعلاني / Advertising Poster', 
            baseHours: 5, 
            basePrice: 125 
        }
    },
    
    // مستويات التعقيد (3 مستويات فقط)
    complexity: {
        'simple': { name: 'بسيط / Simple', multiplier: 1.0 },
        'medium': { name: 'متوسط / Medium', multiplier: 1.5 },
        'complex': { name: 'معقد / Complex', multiplier: 2.0 }
    },
    
    // العملات الأساسية (3 عملات فقط)
    currencies: {
        'USD': { symbol: '$', name: 'US Dollar', nameAr: 'دولار أمريكي', rate: 1.0 },
        'SAR': { symbol: 'ر.س', name: 'Saudi Riyal', nameAr: 'ريال سعودي', rate: 3.75 },
        'AED': { symbol: 'د.إ', name: 'UAE Dirham', nameAr: 'درهم إماراتي', rate: 3.67 }
    }
};

// متغيرات عامة
var currentPrice = 0;
var currentCurrency = 'USD';

// دالة إنشاء الواجهة المبسطة
function createBasicUI() {
    var dialog = new Window('dialog', '💎 حاسبة أسعار التصميم المبسطة - Basic Design Price Calculator');
    dialog.orientation = 'column';
    dialog.alignChildren = 'fill';
    dialog.spacing = 15;
    dialog.margins = 20;
    dialog.preferredSize.width = 500;
    dialog.preferredSize.height = 600;
    
    // العنوان
    var titlePanel = dialog.add('panel');
    titlePanel.orientation = 'column';
    titlePanel.alignChildren = 'center';
    titlePanel.margins = 15;
    
    var titleText = titlePanel.add('statictext', undefined, '💎 حاسبة أسعار التصميم المبسطة');
    titleText.graphics.font = ScriptUI.newFont('Arial', 'BOLD', 16);
    
    var subtitleText = titlePanel.add('statictext', undefined, 'Basic Design Price Calculator v1.0');
    subtitleText.graphics.font = ScriptUI.newFont('Arial', 'REGULAR', 12);
    
    // نوع التصميم
    var designGroup = dialog.add('panel', undefined, '🎨 نوع التصميم / Design Type');
    designGroup.orientation = 'column';
    designGroup.alignChildren = 'fill';
    designGroup.margins = 15;
    
    var designDropdown = designGroup.add('dropdownlist');
    var designKeys = Object.keys(BasicConfig.designTypes);
    for (var i = 0; i < designKeys.length; i++) {
        var key = designKeys[i];
        designDropdown.add('item', BasicConfig.designTypes[key].name);
    }
    designDropdown.selection = 0;
    
    // مستوى التعقيد
    var complexityGroup = dialog.add('panel', undefined, '🔧 مستوى التعقيد / Complexity Level');
    complexityGroup.orientation = 'column';
    complexityGroup.alignChildren = 'fill';
    complexityGroup.margins = 15;
    
    var complexityRadios = {};
    var complexityKeys = Object.keys(BasicConfig.complexity);
    for (var i = 0; i < complexityKeys.length; i++) {
        var key = complexityKeys[i];
        var radio = complexityGroup.add('radiobutton', undefined, BasicConfig.complexity[key].name);
        complexityRadios[key] = radio;
        if (i === 0) radio.value = true; // تحديد الأول كافتراضي
    }
    
    // العملة
    var currencyGroup = dialog.add('panel', undefined, '💱 العملة / Currency');
    currencyGroup.orientation = 'row';
    currencyGroup.alignChildren = 'center';
    currencyGroup.margins = 15;
    
    var currencyRadios = {};
    var currencyKeys = Object.keys(BasicConfig.currencies);
    for (var i = 0; i < currencyKeys.length; i++) {
        var key = currencyKeys[i];
        var currency = BasicConfig.currencies[key];
        var radio = currencyGroup.add('radiobutton', undefined, currency.symbol + ' ' + currency.nameAr);
        currencyRadios[key] = radio;
        if (key === 'USD') radio.value = true; // USD كافتراضي
        
        // ربط حدث تغيير العملة
        (function(currencyCode) {
            radio.onClick = function() {
                currentCurrency = currencyCode;
                if (currentPrice > 0) {
                    updatePriceDisplay();
                }
            };
        })(key);
    }
    
    // النتائج
    var resultsGroup = dialog.add('panel', undefined, '💰 النتائج / Results');
    resultsGroup.orientation = 'column';
    resultsGroup.alignChildren = 'fill';
    resultsGroup.margins = 15;
    
    var priceDisplayGroup = resultsGroup.add('group');
    priceDisplayGroup.orientation = 'row';
    priceDisplayGroup.alignChildren = 'center';
    
    var currencySymbol = priceDisplayGroup.add('statictext', undefined, '$');
    currencySymbol.graphics.font = ScriptUI.newFont('Arial', 'BOLD', 24);
    
    var priceDisplay = priceDisplayGroup.add('statictext', undefined, '0');
    priceDisplay.graphics.font = ScriptUI.newFont('Arial', 'BOLD', 24);
    priceDisplay.preferredSize.width = 150;
    
    // تفاصيل الحساب
    var detailsDisplay = resultsGroup.add('edittext', undefined, '', {multiline: true, readonly: true});
    detailsDisplay.preferredSize.height = 120;
    detailsDisplay.preferredSize.width = 450;
    
    // الأزرار
    var buttonsGroup = dialog.add('group');
    buttonsGroup.orientation = 'row';
    buttonsGroup.alignChildren = 'center';
    buttonsGroup.spacing = 15;
    
    var calculateButton = buttonsGroup.add('button', undefined, '🧮 احسب السعر / Calculate');
    calculateButton.preferredSize.width = 150;
    calculateButton.preferredSize.height = 40;
    
    var exportButton = buttonsGroup.add('button', undefined, '📁 حفظ / Save');
    exportButton.preferredSize.width = 100;
    exportButton.preferredSize.height = 40;
    
    var closeButton = buttonsGroup.add('button', undefined, '❌ إغلاق / Close');
    closeButton.preferredSize.width = 100;
    closeButton.preferredSize.height = 40;
    
    // دالة تحديث عرض السعر
    function updatePriceDisplay() {
        var currency = BasicConfig.currencies[currentCurrency];
        var convertedPrice = currentPrice * currency.rate;
        currencySymbol.text = currency.symbol;
        priceDisplay.text = Math.round(convertedPrice).toString();
    }
    
    // دالة حساب السعر
    function calculatePrice() {
        try {
            // الحصول على نوع التصميم المحدد
            var selectedDesignIndex = designDropdown.selection.index;
            var designKey = designKeys[selectedDesignIndex];
            var designType = BasicConfig.designTypes[designKey];
            
            // الحصول على مستوى التعقيد المحدد
            var selectedComplexity = null;
            for (var key in complexityRadios) {
                if (complexityRadios[key].value) {
                    selectedComplexity = BasicConfig.complexity[key];
                    break;
                }
            }
            
            if (!selectedComplexity) {
                alert('⚠️ يرجى اختيار مستوى التعقيد');
                return 0;
            }
            
            // حساب السعر الأساسي
            var basePrice = designType.basePrice;
            var finalPrice = basePrice * selectedComplexity.multiplier;
            
            // تحديث المتغير العام
            currentPrice = finalPrice;
            
            // تحديث عرض السعر
            updatePriceDisplay();
            
            // تحديث تفاصيل الحساب
            var details = '';
            details += '📋 تفاصيل الحساب / Calculation Details:\n';
            details += '─────────────────────────────────────────\n';
            details += '🎨 نوع التصميم / Design Type: ' + designType.name + '\n';
            details += '💰 السعر الأساسي / Base Price: $' + basePrice + '\n';
            details += '🔧 مستوى التعقيد / Complexity: ' + selectedComplexity.name + '\n';
            details += '📊 معامل التعقيد / Multiplier: x' + selectedComplexity.multiplier + '\n';
            details += '─────────────────────────────────────────\n';
            details += '🎯 السعر النهائي / Final Price: $' + Math.round(finalPrice) + '\n';
            details += '💱 بالعملة المحددة / In Selected Currency: ' + BasicConfig.currencies[currentCurrency].symbol + Math.round(finalPrice * BasicConfig.currencies[currentCurrency].rate);
            
            detailsDisplay.text = details;
            
            return finalPrice;
            
        } catch (error) {
            alert('❌ خطأ في الحساب: ' + error.message);
            return 0;
        }
    }
    
    // دالة حفظ التقدير
    function saveEstimate() {
        if (currentPrice <= 0) {
            alert('⚠️ يرجى حساب السعر أولاً قبل الحفظ');
            return;
        }
        
        try {
            var file = File.saveDialog('حفظ تقدير السعر / Save Price Estimate', '*.txt');
            if (!file) return;
            
            file.open('w');
            file.encoding = 'UTF-8';
            
            // كتابة محتوى الملف
            file.writeln('═══════════════════════════════════════════════════════════');
            file.writeln('            تقدير سعر التصميم / Design Price Estimate            ');
            file.writeln('═══════════════════════════════════════════════════════════');
            file.writeln('');
            
            var now = new Date();
            file.writeln('📅 التاريخ / Date: ' + now.toLocaleDateString());
            file.writeln('🕐 الوقت / Time: ' + now.toLocaleTimeString());
            file.writeln('');
            
            file.writeln('📋 تفاصيل المشروع / Project Details:');
            file.writeln('───────────────────────────────────────────────────────────');
            
            var selectedDesignIndex = designDropdown.selection.index;
            var designKey = designKeys[selectedDesignIndex];
            var designType = BasicConfig.designTypes[designKey];
            
            var selectedComplexity = null;
            for (var key in complexityRadios) {
                if (complexityRadios[key].value) {
                    selectedComplexity = BasicConfig.complexity[key];
                    break;
                }
            }
            
            file.writeln('🎨 نوع التصميم / Design Type: ' + designType.name);
            file.writeln('🔧 مستوى التعقيد / Complexity: ' + selectedComplexity.name);
            file.writeln('💰 السعر الأساسي / Base Price: $' + designType.basePrice);
            file.writeln('📊 معامل التعقيد / Multiplier: x' + selectedComplexity.multiplier);
            file.writeln('');
            
            file.writeln('═══════════════════════════════════════════════════════════');
            file.writeln('🎯 السعر النهائي / Final Price: $' + Math.round(currentPrice));
            var currency = BasicConfig.currencies[currentCurrency];
            var convertedPrice = currentPrice * currency.rate;
            file.writeln('💱 بالعملة المحددة / In Selected Currency: ' + currency.symbol + Math.round(convertedPrice));
            file.writeln('═══════════════════════════════════════════════════════════');
            file.writeln('');
            
            file.writeln('📝 ملاحظات / Notes:');
            file.writeln('• هذا التقدير صالح لمدة 30 يوماً / Valid for 30 days');
            file.writeln('• السعر لا يشمل الضرائب / Price excludes taxes');
            file.writeln('• التعديلات الإضافية قد تتطلب رسوم إضافية');
            file.writeln('  Additional revisions may require extra fees');
            file.writeln('');
            
            file.writeln('───────────────────────────────────────────────────────────');
            file.writeln('تم إنشاؤه بواسطة حاسبة أسعار التصميم المبسطة v1.0');
            file.writeln('Generated by Basic Design Price Calculator v1.0');
            file.writeln('───────────────────────────────────────────────────────────');
            
            file.close();
            
            alert('✅ تم حفظ التقدير بنجاح!\nEstimate saved successfully!\n\nFile: ' + file.fsName);
            
        } catch (error) {
            alert('❌ خطأ في حفظ الملف: ' + error.message);
        }
    }
    
    // ربط الأحداث
    calculateButton.onClick = function() {
        calculatePrice();
    };
    
    exportButton.onClick = function() {
        saveEstimate();
    };
    
    closeButton.onClick = function() {
        dialog.close();
    };
    
    return dialog;
}

// دالة رئيسية لتشغيل الحاسبة
function runBasicCalculator() {
    try {
        // رسالة ترحيب
        alert('🎉 مرحباً بك في حاسبة أسعار التصميم المبسطة v1.0\n\n' +
              '✨ الميزات المتاحة:\n' +
              '🎨 5 أنواع تصميم أساسية\n' +
              '🔧 3 مستويات تعقيد\n' +
              '💱 3 عملات أساسية\n' +
              '📁 حفظ نصي بسيط\n\n' +
              'Welcome to Basic Design Price Calculator v1.0\n\n' +
              '✨ Available Features:\n' +
              '🎨 5 Basic Design Types\n' +
              '🔧 3 Complexity Levels\n' +
              '💱 3 Basic Currencies\n' +
              '📁 Simple Text Export\n\n' +
              'استمتع بالحساب السريع! / Enjoy quick calculations! 🚀');
        
        // إنشاء وعرض الواجهة
        var ui = createBasicUI();
        ui.show();
        
    } catch (error) {
        alert('❌ خطأ في تشغيل الحاسبة: ' + error.message);
    }
}

// تشغيل الحاسبة
runBasicCalculator();
