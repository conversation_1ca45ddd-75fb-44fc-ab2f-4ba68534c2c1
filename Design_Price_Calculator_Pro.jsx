/*
═══════════════════════════════════════════════════════════════════════════════
                    حاسبة أسعار التصميم الاحترافية - الإصدار المتقدم
                   Professional Design Price Calculator - Advanced Version
═══════════════════════════════════════════════════════════════════════════════

الإصدار: 2.0.0
التاريخ: 18 ديسمبر 2024
المطور: Design Price Calculator Team
الترخيص: MIT License

الميزات الجديدة:
✅ تحليل تلقائي للملفات
✅ تخصيص سعر الساعة
✅ دعم عملات متعددة
✅ توليد عرض سعر احترافي
✅ حفظ واسترجاع عروض قديمة
✅ مؤقت لتتبع الوقت
✅ نظام تعديل مرن
✅ إضافة ملاحظات للعميل
✅ ثيم داكن + واجهة احترافية
✅ تصدير متعدد اللغات محسن

═══════════════════════════════════════════════════════════════════════════════
*/

// التحقق من توفر فوتوشوب
if (typeof app === 'undefined' || app.name !== 'Adobe Photoshop') {
    alert('هذا السكريبت يعمل فقط في برنامج Adobe Photoshop');
    throw new Error('Adobe Photoshop required');
}

// إضافة دعم Object.keys للإصدارات القديمة من ExtendScript
if (!Object.keys) {
    Object.keys = function(obj) {
        var keys = [];
        for (var key in obj) {
            if (obj.hasOwnProperty(key)) {
                keys.push(key);
            }
        }
        return keys;
    };
}

// تحميل الملفات المساعدة
try {
    var scriptPath = $.fileName.replace(/[^\/\\]*$/, '');
    
    // تحميل الإعدادات الأساسية
    var configFile = new File(scriptPath + 'pricing_config.jsx');
    if (configFile.exists) {
        $.evalFile(configFile);
    }
    
    // تحميل الميزات المتقدمة
    var advancedFile = new File(scriptPath + 'advanced_features.jsx');
    if (advancedFile.exists) {
        $.evalFile(advancedFile);
    }
    
    // تحميل الواجهة المحسنة
    var uiFile = new File(scriptPath + 'enhanced_ui.jsx');
    if (uiFile.exists) {
        $.evalFile(uiFile);
    }
    
    // تحميل وظائف التصدير
    var exportFile = new File(scriptPath + 'export_functions.jsx');
    if (exportFile.exists) {
        $.evalFile(exportFile);
    } else {
        // تحميل وظائف التصدير المبسطة
        var exportSimpleFile = new File(scriptPath + 'export_functions_simple.jsx');
        if (exportSimpleFile.exists) {
            $.evalFile(exportSimpleFile);
        }
    }

    // تحميل مولد PDF
    var pdfFile = new File(scriptPath + 'pdf_generator.jsx');
    if (pdfFile.exists) {
        $.evalFile(pdfFile);
    } else {
        // تحميل مولد PDF الاحترافي
        var pdfProfessionalFile = new File(scriptPath + 'pdf_generator_professional.jsx');
        if (pdfProfessionalFile.exists) {
            $.evalFile(pdfProfessionalFile);
        } else {
            // تحميل مولد العقود المبسط
            var contractFile = new File(scriptPath + 'contract_generator.jsx');
            if (contractFile.exists) {
                $.evalFile(contractFile);
            }
        }
    }

} catch (e) {
    // في حالة عدم وجود الملفات الإضافية، استخدم الإعدادات المدمجة
    alert('⚠️ بعض الملفات المساعدة غير موجودة. سيتم استخدام الإعدادات الافتراضية.');
}

// إضافة دوال التصدير المدمجة في حالة عدم وجود الملفات الخارجية
if (typeof saveEstimateWithLanguage === 'undefined') {
    // دالة حفظ بسيطة مدمجة
    function saveEstimateWithLanguage(ui, price, language) {
        try {
            language = language || 'arabic';
            var dialogTitle = language === 'english' ?
                'Save Price Estimate' :
                'حفظ تقدير السعر';

            var file = File.saveDialog(dialogTitle, '*.txt');
            if (!file) return false;

            file.open('w');
            file.encoding = 'UTF-8';

            // رأس الملف
            file.writeln('═══════════════════════════════════════════════════════════');
            if (language === 'english') {
                file.writeln('                    Design Price Estimate                    ');
            } else if (language === 'bilingual') {
                file.writeln('            تقدير سعر التصميم / Design Price Estimate            ');
            } else {
                file.writeln('                    تقدير سعر التصميم                    ');
            }
            file.writeln('═══════════════════════════════════════════════════════════');
            file.writeln('');

            // معلومات التاريخ والوقت
            var now = new Date();
            var dateLabel = language === 'english' ? 'Date' : 'التاريخ';
            var timeLabel = language === 'english' ? 'Time' : 'الوقت';

            file.writeln('📅 ' + dateLabel + ': ' + now.toLocaleDateString());
            file.writeln('🕐 ' + timeLabel + ': ' + now.toLocaleTimeString());
            file.writeln('');

            // تفاصيل المشروع
            var projectInfoLabel = language === 'english' ? 'Project Information' : 'معلومات المشروع';
            file.writeln('📋 ' + projectInfoLabel + ':');
            file.writeln('───────────────────────────────────────────────────────────');

            // نوع التصميم
            var designTypeIndex = ui.designTypeDropdown.selection.index;
            var designTypeKeys = Object.keys(PricingConfig.designTypes);
            var designType = PricingConfig.designTypes[designTypeKeys[designTypeIndex]];

            var designTypeLabel = language === 'english' ? 'Design Type' : 'نوع التصميم';
            var hoursLabel = language === 'english' ? 'Hours' : 'عدد الساعات';

            file.writeln('🎨 ' + designTypeLabel + ': ' + designType.name);
            file.writeln('⏰ ' + hoursLabel + ': ' + ui.timeInput.text + (language === 'english' ? ' hours' : ' ساعة'));
            file.writeln('🔧 ' + (language === 'english' ? 'Complexity' : 'درجة التعقيد') + ': ' + ui.complexityValue.text);
            file.writeln('✏️ ' + (language === 'english' ? 'Revisions' : 'عدد التعديلات') + ': ' + ui.revisionsInput.text);
            file.writeln('');

            // السعر النهائي
            var finalPriceLabel = language === 'english' ? 'Final Price' : 'السعر النهائي';
            file.writeln('═══════════════════════════════════════════════════════════');
            file.writeln('🎯 ' + finalPriceLabel + ': ' + ui.currencySymbol.text + Math.round(price));
            file.writeln('═══════════════════════════════════════════════════════════');
            file.writeln('');

            // ملاحظات
            var notesLabel = language === 'english' ? 'Important Notes' : 'ملاحظات مهمة';
            file.writeln('📝 ' + notesLabel + ':');

            if (language === 'english') {
                file.writeln('• This estimate is valid for 30 days from the issue date');
                file.writeln('• Price does not include local taxes if applicable');
                file.writeln('• Additional changes require prior approval');
            } else {
                file.writeln('• هذا التقدير صالح لمدة 30 يوماً من تاريخ الإصدار');
                file.writeln('• السعر لا يشمل الضرائب المحلية إن وجدت');
                file.writeln('• التعديلات الإضافية تتطلب موافقة مسبقة');
            }
            file.writeln('');

            // تذييل
            file.writeln('───────────────────────────────────────────────────────────');
            if (language === 'english') {
                file.writeln('Generated by Design Price Calculator Pro v2.0');
            } else {
                file.writeln('تم إنشاؤه بواسطة حاسبة أسعار التصميم الاحترافية v2.0');
            }
            file.writeln('───────────────────────────────────────────────────────────');

            file.close();

            alert('✅ تم حفظ التقدير بنجاح!\n\nFile saved at:\n' + file.fsName);
            return true;

        } catch (error) {
            alert('❌ خطأ في حفظ الملف: ' + error.message);
            return false;
        }
    }
}

if (typeof saveAsCSVWithLanguage === 'undefined') {
    // دالة حفظ CSV بسيطة مدمجة
    function saveAsCSVWithLanguage(ui, price, language) {
        try {
            language = language || 'arabic';
            var dialogTitle = language === 'english' ?
                'Save Price Estimate - CSV' :
                'حفظ تقدير السعر - CSV';

            var file = File.saveDialog(dialogTitle, '*.csv');
            if (!file) return false;

            file.open('w');
            file.encoding = 'UTF-8';

            // رأس CSV حسب اللغة
            var headers = [];
            if (language === 'english') {
                headers = ['Date', 'Time', 'Design Type', 'Hours', 'Complexity', 'Revisions', 'Final Price'];
            } else {
                headers = ['التاريخ', 'الوقت', 'نوع التصميم', 'الساعات', 'التعقيد', 'التعديلات', 'السعر النهائي'];
            }

            file.writeln(headers.join(','));

            // بيانات التقدير
            var now = new Date();
            var designTypeIndex = ui.designTypeDropdown.selection.index;
            var designTypeKeys = Object.keys(PricingConfig.designTypes);
            var designType = PricingConfig.designTypes[designTypeKeys[designTypeIndex]];

            var csvLine = [
                now.toLocaleDateString(),
                now.toLocaleTimeString(),
                designType.name,
                ui.timeInput.text,
                Math.round(ui.complexitySlider.value),
                ui.revisionsInput.text,
                Math.round(price)
            ].join(',');

            file.writeln(csvLine);
            file.close();

            alert('✅ تم حفظ ملف CSV بنجاح!\n\nFile saved at:\n' + file.fsName);
            return true;

        } catch (error) {
            alert('❌ خطأ في حفظ ملف CSV: ' + error.message);
            return false;
        }
    }
}

if (typeof exportToPDF === 'undefined') {
    // دالة PDF بسيطة مدمجة
    function exportToPDF(ui, price) {
        alert('📄 مولد العقود الاحترافي غير متاح.\n\n' +
              'سيتم حفظ التقدير كملف نصي منسق بدلاً من ذلك.\n\n' +
              'للحصول على ميزة العقود الاحترافية، تأكد من وجود ملف:\n• contract_generator.jsx');

        return saveEstimateWithLanguage(ui, price, 'arabic');
    }
}

// التأكد من وجود إعدادات التسعير الأساسية
if (typeof PricingConfig === 'undefined') {
    var PricingConfig = {
        designTypes: {
            logo: { name: 'لوجو', basePrice: 50 },
            brochure: { name: 'بروشور', basePrice: 40 },
            website: { name: 'موقع إلكتروني', basePrice: 60 }
        },
        complexityMultipliers: {
            simple: { name: 'بسيط', multiplier: 1.0 },
            medium: { name: 'متوسط', multiplier: 1.5 },
            complex: { name: 'معقد', multiplier: 2.0 },
            very_complex: { name: 'معقد جداً', multiplier: 2.5 }
        },
        usageMultipliers: {
            personal: { name: 'شخصي', multiplier: 1.0 },
            small_business: { name: 'مشروع صغير', multiplier: 1.4 },
            commercial: { name: 'تجاري', multiplier: 1.8 },
            enterprise: { name: 'مؤسسي', multiplier: 2.5 }
        },
        deliveryMultipliers: {
            normal: { name: 'عادي', multiplier: 1.0 },
            fast: { name: 'سريع', multiplier: 1.3 },
            urgent: { name: 'مستعجل', multiplier: 1.5 },
            express: { name: 'فوري', multiplier: 2.0 }
        },
        additionalCosts: {
            revisionCost: 15
        },
        settings: {
            minProjectValue: 50,
            maxProjectValue: 10000
        }
    };
}

// متغيرات عامة
var currentPrice = 0;
var currentCurrency = 'USD';
var currentTheme = 'light';

// ═══════════════════════════════════════════════════════════════════════════════
// الدوال الرئيسية المحسنة - ENHANCED MAIN FUNCTIONS
// ═══════════════════════════════════════════════════════════════════════════════

// دالة حساب السعر المحسنة مع دعم العملات
function calculatePriceEnhanced(ui) {
    try {
        // التحقق من صحة البيانات
        if (!ui.designTypeDropdown.selection) {
            ui.statusText.text = '❌ يرجى اختيار نوع التصميم';
            return 0;
        }
        
        ui.statusText.text = '🔄 جاري الحساب...';
        
        // الحصول على القيم من الواجهة
        var designTypeIndex = ui.designTypeDropdown.selection.index;
        var designTypeKeys = Object.keys(PricingConfig.designTypes);
        var designType = PricingConfig.designTypes[designTypeKeys[designTypeIndex]];
        
        var hours = parseFloat(ui.timeInput.text);
        if (isNaN(hours) || hours <= 0) {
            ui.statusText.text = '❌ يرجى إدخال عدد ساعات صحيح';
            ui.priceDisplay.text = '0';
            return 0;
        }
        
        var complexityLevel = Math.round(ui.complexitySlider.value);
        if (isNaN(complexityLevel) || complexityLevel < 1 || complexityLevel > 4) {
            complexityLevel = 1;
        }

        var revisions = parseInt(ui.revisionsInput.text);
        if (isNaN(revisions) || revisions < 0) {
            revisions = 0;
        }
        
        // تحديد نوع الاستخدام
        var usageType = 'personal';
        if (ui.usageSmallBusiness.value) usageType = 'small_business';
        else if (ui.usageCommercial.value) usageType = 'commercial';
        else if (ui.usageEnterprise.value) usageType = 'enterprise';
        
        // تحديد سرعة التسليم
        var deliveryType = 'normal';
        if (ui.deliveryFast.value) deliveryType = 'fast';
        else if (ui.deliveryUrgent.value) deliveryType = 'urgent';
        else if (ui.deliveryExpress.value) deliveryType = 'express';
        
        // حساب السعر الأساسي
        var basePrice = designType.basePrice * hours;
        
        // تطبيق المعاملات
        var complexityKeys = Object.keys(PricingConfig.complexityMultipliers);
        var complexityData = PricingConfig.complexityMultipliers[complexityKeys[complexityLevel - 1]];
        var usageData = PricingConfig.usageMultipliers[usageType];
        var deliveryData = PricingConfig.deliveryMultipliers[deliveryType];

        // التحقق من صحة المعاملات
        if (!complexityData || isNaN(complexityData.multiplier)) {
            complexityData = { name: 'بسيط', multiplier: 1.0 };
        }
        if (!usageData || isNaN(usageData.multiplier)) {
            usageData = { name: 'شخصي', multiplier: 1.0 };
        }
        if (!deliveryData || isNaN(deliveryData.multiplier)) {
            deliveryData = { name: 'عادي', multiplier: 1.0 };
        }
        
        // حساب تكلفة التعديلات
        var freeRevisions = 2;
        var paidRevisions = Math.max(0, revisions - freeRevisions);
        var revisionCost = 15; // قيمة افتراضية
        if (typeof PricingConfig !== 'undefined') {
            if (PricingConfig.additionalCosts && PricingConfig.additionalCosts.revisionCost) {
                revisionCost = PricingConfig.additionalCosts.revisionCost;
            } else if (PricingConfig.revisionCost) {
                revisionCost = PricingConfig.revisionCost;
            }
        }
        var revisionsCost = paidRevisions * revisionCost;
        
        // حساب السعر النهائي
        var priceBeforeRevisions = basePrice * complexityData.multiplier * usageData.multiplier * deliveryData.multiplier;
        var finalPrice = priceBeforeRevisions + revisionsCost;
        
        // تطبيق الحدود
        finalPrice = Math.max(PricingConfig.settings.minProjectValue, finalPrice);
        finalPrice = Math.min(PricingConfig.settings.maxProjectValue, finalPrice);
        
        // تحويل العملة
        if (typeof AdvancedConfig !== 'undefined' && AdvancedConfig.currencies[currentCurrency]) {
            finalPrice = finalPrice * AdvancedConfig.currencies[currentCurrency].rate;
            ui.currencySymbol.text = AdvancedConfig.currencies[currentCurrency].symbol;
        } else {
            ui.currencySymbol.text = '$';
        }

        // التأكد من أن السعر رقم صحيح
        if (isNaN(finalPrice) || finalPrice <= 0) {
            finalPrice = 0;
            ui.statusText.text = '❌ خطأ في حساب السعر - تحقق من البيانات';
        } else {
            ui.statusText.text = '✅ تم حساب السعر بنجاح';
        }

        // تحديث العرض
        ui.priceDisplay.text = Math.round(finalPrice).toString();

        // تحديث المتغير العام
        currentPrice = finalPrice;

        // عرض التفاصيل
        var details = '📊 تفاصيل حساب السعر:\n\n';
        details += '🎨 نوع التصميم: ' + designType.name + ' ($' + designType.basePrice + '/ساعة)\n';
        details += '⏰ عدد الساعات: ' + hours + ' ساعة\n';
        details += '💵 السعر الأساسي: $' + Math.round(basePrice) + '\n\n';
        details += '🔧 معامل التعقيد: ' + complexityData.name + ' (x' + complexityData.multiplier + ')\n';
        details += '👥 معامل الاستخدام: ' + usageData.name + ' (x' + usageData.multiplier + ')\n';
        details += '🚀 معامل التسليم: ' + deliveryData.name + ' (x' + deliveryData.multiplier + ')\n\n';
        details += '💰 السعر بعد المعاملات: $' + Math.round(priceBeforeRevisions) + '\n';
        
        if (paidRevisions > 0) {
            details += '✏️ تعديلات إضافية: ' + paidRevisions + ' × $' + revisionCost + ' = $' + revisionsCost + '\n';
        } else {
            details += '✏️ التعديلات: ' + revisions + ' (مشمولة مجاناً)\n';
        }
        
        details += '\n🎯 السعر النهائي: ' + ui.currencySymbol.text + Math.round(finalPrice);

        if (currentCurrency !== 'USD' && typeof AdvancedConfig !== 'undefined' && AdvancedConfig.currencies[currentCurrency]) {
            details += '\n💱 العملة: ' + AdvancedConfig.currencies[currentCurrency].nameAr;
        }
        
        ui.detailsDisplay.text = details;
        
        return finalPrice;
        
    } catch (error) {
        ui.statusText.text = '❌ خطأ في الحساب';
        alert('خطأ في حساب السعر: ' + error.message);
        return 0;
    }
}

// دالة إعادة تعيين النموذج المحسنة
function resetFormEnhanced(ui) {
    try {
        ui.statusText.text = '🔄 جاري إعادة التعيين...';
        
        // إعادة تعيين القيم
        ui.designTypeDropdown.selection = 0;
        ui.timeInput.text = '5';
        ui.timeSlider.value = 5;
        ui.complexitySlider.value = 1;
        ui.complexitySlider.onChanging();
        ui.revisionsInput.text = '2';
        
        // إعادة تعيين أزرار الاختيار
        ui.usagePersonal.value = true;
        ui.usageSmallBusiness.value = false;
        ui.usageCommercial.value = false;
        ui.usageEnterprise.value = false;
        
        ui.deliveryNormal.value = true;
        ui.deliveryFast.value = false;
        ui.deliveryUrgent.value = false;
        ui.deliveryExpress.value = false;
        
        // مسح النتائج
        ui.priceDisplay.text = '0';
        ui.detailsDisplay.text = '';
        ui.statusText.text = '✨ تم إعادة التعيين - جاهز للحساب';
        
        currentPrice = 0;
        
    } catch (error) {
        ui.statusText.text = '❌ خطأ في إعادة التعيين';
        alert('خطأ في إعادة التعيين: ' + error.message);
    }
}

// دالة تبديل الثيم
function toggleTheme(ui) {
    currentTheme = currentTheme === 'light' ? 'dark' : 'light';
    ui.themeButton.text = currentTheme === 'dark' ? '☀️ ثيم فاتح' : '🌙 ثيم داكن';
    
    // تطبيق الثيم (محدود في ExtendScript)
    try {
        if (typeof applyTheme === 'function') {
            applyTheme(ui.dialog, currentTheme);
        }
    } catch (e) {
        // تجاهل أخطاء تطبيق الثيم
    }
    
    ui.statusText.text = '🎨 تم تغيير الثيم إلى ' + (currentTheme === 'dark' ? 'داكن' : 'فاتح');
}

// ═══════════════════════════════════════════════════════════════════════════════
// الدالة الرئيسية المحسنة - ENHANCED MAIN FUNCTION
// ═══════════════════════════════════════════════════════════════════════════════

function mainEnhanced() {
    try {
        // إنشاء الواجهة المحسنة
        var ui;
        if (typeof createEnhancedUI === 'function') {
            ui = createEnhancedUI();
        } else {
            alert('❌ ملف الواجهة المحسنة غير موجود. سيتم استخدام الواجهة الأساسية.');
            return;
        }
        
        // ربط أحداث الحساب
        ui.calculateButton.onClick = function() {
            currentPrice = calculatePriceEnhanced(ui);
            // تشخيص للتأكد من تحديث السعر
            if (currentPrice > 0) {
                ui.statusText.text = '✅ تم حساب السعر: $' + Math.round(currentPrice);
            }
        };
        
        // ربط أحداث إعادة التعيين
        ui.resetButton.onClick = function() {
            var result = confirm('هل أنت متأكد من إعادة تعيين جميع البيانات؟');
            if (result) {
                resetFormEnhanced(ui);
            }
        };
        
        // ربط أحداث الثيم
        ui.themeButton.onClick = function() {
            toggleTheme(ui);
        };
        
        // ربط أحداث العملة
        ui.currencyButton.onClick = function() {
            if (typeof showCurrencyManager === 'function') {
                var result = showCurrencyManager();
                if (result.selected) {
                    currentCurrency = result.currency;
                    ui.statusText.text = '💱 تم تغيير العملة إلى ' + AdvancedConfig.currencies[currentCurrency].nameAr;
                    // إعادة حساب السعر إذا كان موجود
                    if (currentPrice > 0) {
                        calculatePriceEnhanced(ui);
                    }
                }
            } else {
                alert('ميزة إدارة العملات غير متاحة');
            }
        };

        // ربط أحداث الإعدادات
        ui.settingsButton.onClick = function() {
            if (typeof showAdvancedPriceCustomizer === 'function') {
                var result = showAdvancedPriceCustomizer();
                if (result.saved) {
                    ui.statusText.text = '⚙️ تم تحديث إعدادات التسعير';
                    // إعادة حساب السعر إذا كان موجود
                    if (currentPrice > 0) {
                        calculatePriceEnhanced(ui);
                    }
                }
            } else {
                alert('ميزة الإعدادات المتقدمة غير متاحة - تأكد من وجود ملف price_customizer.jsx');
            }
        };
        
        // ربط أحداث التحليل التلقائي
        ui.analyzeButton.onClick = function() {
            if (typeof analyzeDesignFile === 'function') {
                var analysis = analyzeDesignFile();
                if (analysis) {
                    var result = showAnalysisResults(analysis);
                    if (result.apply) {
                        ui.timeInput.text = Math.round(result.hours).toString();
                        ui.timeSlider.value = result.hours;
                        ui.analysisText.text = '✅ تم تطبيق نتائج التحليل: ' + analysis.layers + ' طبقة، ' + Math.round(result.hours) + ' ساعة';
                        ui.statusText.text = '🧠 تم تحليل الملف وتطبيق النتائج';
                    }
                }
            } else {
                alert('ميزة التحليل التلقائي غير متاحة');
            }
        };
        

        
        // ربط أحداث السجل
        ui.historyButton.onClick = function() {
            if (typeof showProjectHistory === 'function') {
                showProjectHistory();
            } else {
                alert('ميزة سجل المشاريع غير متاحة');
            }
        };
        
        // ربط أحداث الملاحظات
        ui.notesButton.onClick = function() {
            if (typeof manageClientNotes === 'function') {
                var result = manageClientNotes();
                if (result.saved) {
                    ui.statusText.text = '💬 تم حفظ ملاحظات العميل: ' + result.clientName;
                }
            } else {
                alert('ميزة ملاحظات العميل غير متاحة');
            }
        };
        
        // ربط أحداث التصدير
        ui.exportArabicBtn.onClick = function() {
            if (currentPrice > 0) {
                if (typeof saveEstimateWithLanguage === 'function') {
                    saveEstimateWithLanguage(ui, currentPrice, 'arabic');
                } else {
                    alert('❌ وظائف التصدير غير متاحة\n\nتأكد من وجود الملفات التالية:\n• export_functions.jsx\n• export_functions_simple.jsx');
                }
            } else {
                alert('⚠️ يرجى حساب السعر أولاً قبل التصدير');
            }
        };
        
        ui.exportEnglishBtn.onClick = function() {
            if (currentPrice > 0) {
                if (typeof saveEstimateWithLanguage === 'function') {
                    saveEstimateWithLanguage(ui, currentPrice, 'english');
                } else {
                    alert('❌ وظائف التصدير غير متاحة\n\nتأكد من وجود الملفات التالية:\n• export_functions.jsx\n• export_functions_simple.jsx');
                }
            } else {
                alert('⚠️ يرجى حساب السعر أولاً قبل التصدير');
            }
        };
        
        ui.exportBilingualBtn.onClick = function() {
            if (currentPrice > 0) {
                if (typeof saveEstimateWithLanguage === 'function') {
                    saveEstimateWithLanguage(ui, currentPrice, 'bilingual');
                } else {
                    alert('❌ وظائف التصدير غير متاحة\n\nتأكد من وجود الملفات التالية:\n• export_functions.jsx\n• export_functions_simple.jsx');
                }
            } else {
                alert('⚠️ يرجى حساب السعر أولاً قبل التصدير');
            }
        };
        
        ui.exportCSVBtn.onClick = function() {
            if (currentPrice > 0) {
                if (typeof saveAsCSVWithLanguage === 'function') {
                    saveAsCSVWithLanguage(ui, currentPrice, 'arabic');
                } else {
                    alert('❌ وظائف تصدير CSV غير متاحة\n\nتأكد من وجود الملفات التالية:\n• export_functions.jsx\n• export_functions_simple.jsx');
                }
            } else {
                alert('⚠️ يرجى حساب السعر أولاً قبل التصدير');
            }
        };
        
        ui.exportPDFBtn.onClick = function() {
            if (currentPrice > 0) {
                if (typeof exportToPDF === 'function') {
                    exportToPDF(ui, currentPrice);
                } else {
                    alert('📄 مولد العقود الاحترافي غير متاح\n\nتأكد من وجود الملفات التالية:\n• contract_generator.jsx\n• pdf_generator_professional.jsx\n• export_functions_simple.jsx\n\nسيتم حفظ التقدير كملف نصي منسق بدلاً من ذلك.');
                    // محاولة حفظ كملف نصي
                    if (typeof saveEstimateWithLanguage === 'function') {
                        saveEstimateWithLanguage(ui, currentPrice, 'arabic');
                    }
                }
            } else {
                alert('⚠️ يرجى حساب السعر أولاً قبل التصدير');
            }
        };
        
        // ربط أحداث الإغلاق
        ui.closeButton.onClick = function() {
            ui.dialog.close();
        };
        
        // رسالة ترحيب
        ui.statusText.text = '🎉 مرحباً بك في النسخة الاحترافية!';
        
        // عرض الواجهة
        ui.dialog.show();
        
    } catch (error) {
        alert('❌ خطأ في تشغيل السكريبت الاحترافي: ' + error.message);
    }
}

// ═══════════════════════════════════════════════════════════════════════════════
// تشغيل السكريبت - RUN SCRIPT
// ═══════════════════════════════════════════════════════════════════════════════

// رسالة ترحيب محسنة
alert('🎉 مرحباً بك في حاسبة أسعار التصميم الاحترافية v2.0\n\n' +
      '✨ الميزات الجديدة:\n' +
      '🧠 تحليل تلقائي للملفات\n' +
      '🌍 دعم عملات متعددة\n' +
      '⚙️ تخصيص سعر الساعة المتقدم\n' +
      '🌙 ثيم داكن احترافي\n' +
      '📊 تصدير محسن متعدد اللغات\n' +
      '💬 ملاحظات العميل\n' +
      '🧾 سجل المشاريع\n' +
      '📄 مولد عروض PDF احترافية\n\n' +
      'استمتع بالتجربة الاحترافية! 🚀');

// تشغيل السكريبت الاحترافي
mainEnhanced();

/*
═══════════════════════════════════════════════════════════════════════════════
                                 نهاية السكريبت
                                 END OF SCRIPT
═══════════════════════════════════════════════════════════════════════════════
*/
