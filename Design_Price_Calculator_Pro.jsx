/*
═══════════════════════════════════════════════════════════════════════════════
                    حاسبة أسعار التصميم الاحترافية - الإصدار المتقدم
                   Professional Design Price Calculator - Advanced Version
═══════════════════════════════════════════════════════════════════════════════

الإصدار: 2.0.0
التاريخ: 18 ديسمبر 2024
المطور: Design Price Calculator Team
الترخيص: MIT License

الميزات الجديدة:
✅ تحليل تلقائي للملفات
✅ تخصيص سعر الساعة
✅ دعم عملات متعددة
✅ توليد عرض سعر احترافي
✅ حفظ واسترجاع عروض قديمة
✅ مؤقت لتتبع الوقت
✅ نظام تعديل مرن
✅ إضافة ملاحظات للعميل
✅ ثيم داكن + واجهة احترافية
✅ تصدير متعدد اللغات محسن

═══════════════════════════════════════════════════════════════════════════════
*/

// التحقق من توفر فوتوشوب
if (typeof app === 'undefined' || app.name !== 'Adobe Photoshop') {
    alert('هذا السكريبت يعمل فقط في برنامج Adobe Photoshop');
    throw new Error('Adobe Photoshop required');
}

// إضافة دعم Object.keys للإصدارات القديمة من ExtendScript
if (!Object.keys) {
    Object.keys = function(obj) {
        var keys = [];
        for (var key in obj) {
            if (obj.hasOwnProperty(key)) {
                keys.push(key);
            }
        }
        return keys;
    };
}

// تحميل الملفات المساعدة
try {
    var scriptPath = $.fileName.replace(/[^\/\\]*$/, '');
    
    // تحميل الإعدادات الأساسية
    var configFile = new File(scriptPath + 'pricing_config.jsx');
    if (configFile.exists) {
        $.evalFile(configFile);
    }
    
    // تحميل الميزات المتقدمة
    var advancedFile = new File(scriptPath + 'advanced_features.jsx');
    if (advancedFile.exists) {
        $.evalFile(advancedFile);
    }
    
    // تحميل الواجهة المحسنة
    var uiFile = new File(scriptPath + 'enhanced_ui.jsx');
    if (uiFile.exists) {
        $.evalFile(uiFile);
    }
    




} catch (e) {
    // في حالة عدم وجود الملفات الإضافية، استخدم الإعدادات المدمجة
    alert('⚠️ بعض الملفات المساعدة غير موجودة. سيتم استخدام الإعدادات الافتراضية.');
}

// إضافة دالة التصدير المدمجة
if (typeof saveEstimateWithLanguage === 'undefined') {
    // دالة حفظ بسيطة مدمجة
    function saveEstimateWithLanguage(ui, price, language) {
        try {
            language = language || 'arabic';
            var dialogTitle = language === 'english' ?
                'Save Price Estimate' :
                language === 'bilingual' ?
                'حفظ تقدير السعر / Save Price Estimate' :
                'حفظ تقدير السعر';

            var file = File.saveDialog(dialogTitle, '*.txt');
            if (!file) return false;

            file.open('w');
            file.encoding = 'UTF-8';

            // رأس الملف
            file.writeln('═══════════════════════════════════════════════════════════');
            if (language === 'english') {
                file.writeln('                    Design Price Estimate                    ');
            } else if (language === 'bilingual') {
                file.writeln('            تقدير سعر التصميم / Design Price Estimate            ');
            } else {
                file.writeln('                    تقدير سعر التصميم                    ');
            }
            file.writeln('═══════════════════════════════════════════════════════════');
            file.writeln('');

            // معلومات التاريخ والوقت
            var now = new Date();
            if (language === 'english') {
                file.writeln('📅 Date: ' + now.toLocaleDateString());
                file.writeln('🕐 Time: ' + now.toLocaleTimeString());
            } else if (language === 'bilingual') {
                file.writeln('📅 التاريخ / Date: ' + now.toLocaleDateString());
                file.writeln('🕐 الوقت / Time: ' + now.toLocaleTimeString());
            } else {
                file.writeln('📅 التاريخ: ' + now.toLocaleDateString());
                file.writeln('🕐 الوقت: ' + now.toLocaleTimeString());
            }
            file.writeln('');

            // تفاصيل المشروع
            if (language === 'english') {
                file.writeln('📋 Project Information:');
            } else if (language === 'bilingual') {
                file.writeln('📋 معلومات المشروع / Project Information:');
            } else {
                file.writeln('📋 معلومات المشروع:');
            }
            file.writeln('───────────────────────────────────────────────────────────');

            // نوع التصميم
            var designTypeIndex = ui.designTypeDropdown.selection.index;
            var designTypeKeys = Object.keys(PricingConfig.designTypes);
            var designType = PricingConfig.designTypes[designTypeKeys[designTypeIndex]];

            if (language === 'english') {
                file.writeln('🎨 Design Type: ' + designType.name);
                file.writeln('⏰ Hours: ' + ui.timeInput.text + ' hours');
                file.writeln('🔧 Complexity: ' + ui.complexityValue.text);
                file.writeln('✏️ Revisions: ' + ui.revisionsInput.text);
            } else if (language === 'bilingual') {
                file.writeln('🎨 نوع التصميم / Design Type: ' + designType.name);
                file.writeln('⏰ عدد الساعات / Hours: ' + ui.timeInput.text + ' ساعة / hours');
                file.writeln('🔧 درجة التعقيد / Complexity: ' + ui.complexityValue.text);
                file.writeln('✏️ عدد التعديلات / Revisions: ' + ui.revisionsInput.text);
            } else {
                file.writeln('🎨 نوع التصميم: ' + designType.name);
                file.writeln('⏰ عدد الساعات: ' + ui.timeInput.text + ' ساعة');
                file.writeln('🔧 درجة التعقيد: ' + ui.complexityValue.text);
                file.writeln('✏️ عدد التعديلات: ' + ui.revisionsInput.text);
            }
            file.writeln('');

            // السعر النهائي
            file.writeln('═══════════════════════════════════════════════════════════');
            if (language === 'english') {
                file.writeln('🎯 Final Price: ' + ui.currencySymbol.text + Math.round(price));
            } else if (language === 'bilingual') {
                file.writeln('🎯 السعر النهائي / Final Price: ' + ui.currencySymbol.text + Math.round(price));
            } else {
                file.writeln('🎯 السعر النهائي: ' + ui.currencySymbol.text + Math.round(price));
            }
            file.writeln('═══════════════════════════════════════════════════════════');
            file.writeln('');

            // معلومات العميل والملاحظات المخصصة
            if (clientName || clientNotes) {
                if (language === 'english') {
                    file.writeln('👤 Client Information:');
                } else if (language === 'bilingual') {
                    file.writeln('👤 معلومات العميل / Client Information:');
                } else {
                    file.writeln('👤 معلومات العميل:');
                }
                file.writeln('───────────────────────────────────────────────────────────');

                if (clientName) {
                    if (language === 'english') {
                        file.writeln('📋 Client Name: ' + clientName);
                    } else if (language === 'bilingual') {
                        file.writeln('📋 اسم العميل / Client Name: ' + clientName);
                    } else {
                        file.writeln('📋 اسم العميل: ' + clientName);
                    }
                }

                if (clientNotes) {
                    if (language === 'english') {
                        file.writeln('💬 Notes: ' + clientNotes);
                    } else if (language === 'bilingual') {
                        file.writeln('💬 ملاحظات / Notes: ' + clientNotes);
                    } else {
                        file.writeln('💬 ملاحظات: ' + clientNotes);
                    }
                }
                file.writeln('');
            }

            // ملاحظات عامة
            if (language === 'english') {
                file.writeln('📝 Important Notes:');
                file.writeln('• This estimate is valid for 30 days from the issue date');
                file.writeln('• Price does not include local taxes if applicable');
                file.writeln('• Additional changes require prior approval');
            } else if (language === 'bilingual') {
                file.writeln('📝 ملاحظات مهمة / Important Notes:');
                file.writeln('• هذا التقدير صالح لمدة 30 يوماً من تاريخ الإصدار');
                file.writeln('  This estimate is valid for 30 days from the issue date');
                file.writeln('• السعر لا يشمل الضرائب المحلية إن وجدت');
                file.writeln('  Price does not include local taxes if applicable');
                file.writeln('• التعديلات الإضافية تتطلب موافقة مسبقة');
                file.writeln('  Additional changes require prior approval');
            } else {
                file.writeln('📝 ملاحظات مهمة:');
                file.writeln('• هذا التقدير صالح لمدة 30 يوماً من تاريخ الإصدار');
                file.writeln('• السعر لا يشمل الضرائب المحلية إن وجدت');
                file.writeln('• التعديلات الإضافية تتطلب موافقة مسبقة');
            }
            file.writeln('');

            // تذييل
            file.writeln('───────────────────────────────────────────────────────────');
            if (language === 'english') {
                file.writeln('Generated by Design Price Calculator Pro v2.0');
            } else if (language === 'bilingual') {
                file.writeln('تم إنشاؤه بواسطة حاسبة أسعار التصميم الاحترافية v2.0');
                file.writeln('Generated by Design Price Calculator Pro v2.0');
            } else {
                file.writeln('تم إنشاؤه بواسطة حاسبة أسعار التصميم الاحترافية v2.0');
            }
            file.writeln('───────────────────────────────────────────────────────────');

            file.close();

            var successMsg = language === 'english' ?
                '✅ Estimate saved successfully!\n\nFile saved at:\n' + file.fsName :
                language === 'bilingual' ?
                '✅ تم حفظ التقدير بنجاح! / Estimate saved successfully!\n\nFile saved at:\n' + file.fsName :
                '✅ تم حفظ التقدير بنجاح!\n\nFile saved at:\n' + file.fsName;

            alert(successMsg);
            return true;

        } catch (error) {
            alert('❌ خطأ في حفظ الملف: ' + error.message);
            return false;
        }
    }
}





// التأكد من وجود إعدادات التسعير الأساسية
if (typeof PricingConfig === 'undefined') {
    var PricingConfig = {
        designTypes: {
            logo: { name: 'لوجو', basePrice: 50 },
            brochure: { name: 'بروشور', basePrice: 40 },
            website: { name: 'موقع إلكتروني', basePrice: 60 }
        },
        complexityMultipliers: {
            simple: { name: 'بسيط', multiplier: 1.0 },
            medium: { name: 'متوسط', multiplier: 1.5 },
            complex: { name: 'معقد', multiplier: 2.0 },
            very_complex: { name: 'معقد جداً', multiplier: 2.5 }
        },
        usageMultipliers: {
            personal: { name: 'شخصي', multiplier: 1.0 },
            small_business: { name: 'مشروع صغير', multiplier: 1.4 },
            commercial: { name: 'تجاري', multiplier: 1.8 },
            enterprise: { name: 'مؤسسي', multiplier: 2.5 }
        },
        deliveryMultipliers: {
            normal: { name: 'عادي', multiplier: 1.0 },
            fast: { name: 'سريع', multiplier: 1.3 },
            urgent: { name: 'مستعجل', multiplier: 1.5 },
            express: { name: 'فوري', multiplier: 2.0 }
        },
        additionalCosts: {
            revisionCost: 15
        },
        settings: {
            minProjectValue: 50,
            maxProjectValue: 10000
        }
    };
}

// متغيرات عامة
var currentPrice = 0;
var currentCurrency = 'USD';
var clientName = '';
var clientNotes = '';

// ═══════════════════════════════════════════════════════════════════════════════
// الدوال الرئيسية المحسنة - ENHANCED MAIN FUNCTIONS
// ═══════════════════════════════════════════════════════════════════════════════

// دالة حساب السعر المحسنة مع دعم العملات
function calculatePriceEnhanced(ui) {
    try {
        // التحقق من صحة البيانات
        if (!ui.designTypeDropdown.selection) {
            ui.statusText.text = '❌ يرجى اختيار نوع التصميم';
            return 0;
        }
        
        ui.statusText.text = '🔄 جاري الحساب...';
        
        // الحصول على القيم من الواجهة
        var designTypeIndex = ui.designTypeDropdown.selection.index;
        var designTypeKeys = Object.keys(PricingConfig.designTypes);
        var designType = PricingConfig.designTypes[designTypeKeys[designTypeIndex]];
        
        var hours = parseFloat(ui.timeInput.text);
        if (isNaN(hours) || hours <= 0) {
            ui.statusText.text = '❌ يرجى إدخال عدد ساعات صحيح';
            ui.priceDisplay.text = '0';
            return 0;
        }
        
        var complexityLevel = Math.round(ui.complexitySlider.value);
        if (isNaN(complexityLevel) || complexityLevel < 1 || complexityLevel > 4) {
            complexityLevel = 1;
        }

        var revisions = parseInt(ui.revisionsInput.text);
        if (isNaN(revisions) || revisions < 0) {
            revisions = 0;
        }
        
        // تحديد نوع الاستخدام
        var usageType = 'personal';
        if (ui.usageSmallBusiness.value) usageType = 'small_business';
        else if (ui.usageCommercial.value) usageType = 'commercial';
        else if (ui.usageEnterprise.value) usageType = 'enterprise';
        
        // تحديد سرعة التسليم
        var deliveryType = 'normal';
        if (ui.deliveryFast.value) deliveryType = 'fast';
        else if (ui.deliveryUrgent.value) deliveryType = 'urgent';
        else if (ui.deliveryExpress.value) deliveryType = 'express';
        
        // حساب السعر الأساسي
        var basePrice = designType.basePrice * hours;
        
        // تطبيق المعاملات
        var complexityKeys = Object.keys(PricingConfig.complexityMultipliers);
        var complexityData = PricingConfig.complexityMultipliers[complexityKeys[complexityLevel - 1]];
        var usageData = PricingConfig.usageMultipliers[usageType];
        var deliveryData = PricingConfig.deliveryMultipliers[deliveryType];

        // التحقق من صحة المعاملات
        if (!complexityData || isNaN(complexityData.multiplier)) {
            complexityData = { name: 'بسيط', multiplier: 1.0 };
        }
        if (!usageData || isNaN(usageData.multiplier)) {
            usageData = { name: 'شخصي', multiplier: 1.0 };
        }
        if (!deliveryData || isNaN(deliveryData.multiplier)) {
            deliveryData = { name: 'عادي', multiplier: 1.0 };
        }
        
        // حساب تكلفة التعديلات
        var freeRevisions = 2;
        var paidRevisions = Math.max(0, revisions - freeRevisions);
        var revisionCost = 15; // قيمة افتراضية
        if (typeof PricingConfig !== 'undefined') {
            if (PricingConfig.additionalCosts && PricingConfig.additionalCosts.revisionCost) {
                revisionCost = PricingConfig.additionalCosts.revisionCost;
            } else if (PricingConfig.revisionCost) {
                revisionCost = PricingConfig.revisionCost;
            }
        }
        var revisionsCost = paidRevisions * revisionCost;
        
        // حساب السعر النهائي
        var priceBeforeRevisions = basePrice * complexityData.multiplier * usageData.multiplier * deliveryData.multiplier;
        var finalPrice = priceBeforeRevisions + revisionsCost;
        
        // تطبيق الحدود
        finalPrice = Math.max(PricingConfig.settings.minProjectValue, finalPrice);
        finalPrice = Math.min(PricingConfig.settings.maxProjectValue, finalPrice);
        
        // تحويل العملة
        if (typeof AdvancedConfig !== 'undefined' && AdvancedConfig.currencies[currentCurrency]) {
            finalPrice = finalPrice * AdvancedConfig.currencies[currentCurrency].rate;
            ui.currencySymbol.text = AdvancedConfig.currencies[currentCurrency].symbol;
        } else {
            ui.currencySymbol.text = '$';
        }

        // التأكد من أن السعر رقم صحيح
        if (isNaN(finalPrice) || finalPrice <= 0) {
            finalPrice = 0;
            ui.statusText.text = '❌ خطأ في حساب السعر - تحقق من البيانات';
        } else {
            ui.statusText.text = '✅ تم حساب السعر بنجاح';
        }

        // تحديث العرض
        ui.priceDisplay.text = Math.round(finalPrice).toString();

        // تحديث المتغير العام
        currentPrice = finalPrice;

        // عرض التفاصيل
        var details = '📊 تفاصيل حساب السعر:\n\n';
        details += '🎨 نوع التصميم: ' + designType.name + ' ($' + designType.basePrice + '/ساعة)\n';
        details += '⏰ عدد الساعات: ' + hours + ' ساعة\n';
        details += '💵 السعر الأساسي: $' + Math.round(basePrice) + '\n\n';
        details += '🔧 معامل التعقيد: ' + complexityData.name + ' (x' + complexityData.multiplier + ')\n';
        details += '👥 معامل الاستخدام: ' + usageData.name + ' (x' + usageData.multiplier + ')\n';
        details += '🚀 معامل التسليم: ' + deliveryData.name + ' (x' + deliveryData.multiplier + ')\n\n';
        details += '💰 السعر بعد المعاملات: $' + Math.round(priceBeforeRevisions) + '\n';
        
        if (paidRevisions > 0) {
            details += '✏️ تعديلات إضافية: ' + paidRevisions + ' × $' + revisionCost + ' = $' + revisionsCost + '\n';
        } else {
            details += '✏️ التعديلات: ' + revisions + ' (مشمولة مجاناً)\n';
        }
        
        details += '\n🎯 السعر النهائي: ' + ui.currencySymbol.text + Math.round(finalPrice);

        if (currentCurrency !== 'USD' && typeof AdvancedConfig !== 'undefined' && AdvancedConfig.currencies[currentCurrency]) {
            details += '\n💱 العملة: ' + AdvancedConfig.currencies[currentCurrency].nameAr;
        }
        
        ui.detailsDisplay.text = details;
        
        return finalPrice;
        
    } catch (error) {
        ui.statusText.text = '❌ خطأ في الحساب';
        alert('خطأ في حساب السعر: ' + error.message);
        return 0;
    }
}

// دالة إعادة تعيين النموذج المحسنة
function resetFormEnhanced(ui) {
    try {
        ui.statusText.text = '🔄 جاري إعادة التعيين...';
        
        // إعادة تعيين القيم
        ui.designTypeDropdown.selection = 0;
        ui.timeInput.text = '5';
        ui.timeSlider.value = 5;
        ui.complexitySlider.value = 1;
        ui.complexitySlider.onChanging();
        ui.revisionsInput.text = '2';
        
        // إعادة تعيين أزرار الاختيار
        ui.usagePersonal.value = true;
        ui.usageSmallBusiness.value = false;
        ui.usageCommercial.value = false;
        ui.usageEnterprise.value = false;
        
        ui.deliveryNormal.value = true;
        ui.deliveryFast.value = false;
        ui.deliveryUrgent.value = false;
        ui.deliveryExpress.value = false;
        
        // مسح النتائج
        ui.priceDisplay.text = '0';
        ui.detailsDisplay.text = '';
        ui.statusText.text = '✨ تم إعادة التعيين - جاهز للحساب';
        
        currentPrice = 0;
        
    } catch (error) {
        ui.statusText.text = '❌ خطأ في إعادة التعيين';
        alert('خطأ في إعادة التعيين: ' + error.message);
    }
}

// دالة بسيطة لإدخال ملاحظات العميل
function showSimpleNotesDialog() {
    try {
        var dialog = new Window('dialog', '💬 ملاحظات العميل - Client Notes');
        dialog.orientation = 'column';
        dialog.alignChildren = 'fill';
        dialog.spacing = 15;
        dialog.margins = 20;
        dialog.preferredSize.width = 400;

        // اسم العميل
        var nameGroup = dialog.add('group');
        nameGroup.orientation = 'row';
        nameGroup.alignChildren = 'center';

        var nameLabel = nameGroup.add('statictext', undefined, '👤 اسم العميل:');
        nameLabel.preferredSize.width = 80;

        var nameInput = nameGroup.add('edittext', undefined, clientName);
        nameInput.preferredSize.width = 250;

        // الملاحظات
        var notesGroup = dialog.add('group');
        notesGroup.orientation = 'column';
        notesGroup.alignChildren = 'fill';

        var notesLabel = notesGroup.add('statictext', undefined, '💬 ملاحظات:');
        var notesInput = notesGroup.add('edittext', undefined, clientNotes, {multiline: true});
        notesInput.preferredSize.height = 100;
        notesInput.preferredSize.width = 350;

        // الأزرار
        var buttonsGroup = dialog.add('group');
        buttonsGroup.orientation = 'row';
        buttonsGroup.alignChildren = 'center';

        var saveButton = buttonsGroup.add('button', undefined, '💾 حفظ');
        var cancelButton = buttonsGroup.add('button', undefined, '❌ إلغاء');

        // ربط الأحداث
        saveButton.onClick = function() {
            clientName = nameInput.text || '';
            clientNotes = notesInput.text || '';
            dialog.close(1);
        };

        cancelButton.onClick = function() {
            dialog.close(0);
        };

        // عرض النافذة
        var result = dialog.show();

        if (result === 1) {
            return {
                saved: true,
                clientName: clientName,
                notes: clientNotes
            };
        } else {
            return {
                saved: false
            };
        }

    } catch (error) {
        alert('❌ خطأ في نافذة الملاحظات: ' + error.message);
        return { saved: false };
    }
}

// ═══════════════════════════════════════════════════════════════════════════════
// الدالة الرئيسية المحسنة - ENHANCED MAIN FUNCTION
// ═══════════════════════════════════════════════════════════════════════════════

function mainEnhanced() {
    try {
        // إنشاء الواجهة المحسنة
        var ui;
        if (typeof createEnhancedUI === 'function') {
            ui = createEnhancedUI();
        } else {
            alert('❌ ملف الواجهة المحسنة غير موجود. سيتم استخدام الواجهة الأساسية.');
            return;
        }
        
        // ربط أحداث الحساب
        ui.calculateButton.onClick = function() {
            currentPrice = calculatePriceEnhanced(ui);
            // تشخيص للتأكد من تحديث السعر
            if (currentPrice > 0) {
                ui.statusText.text = '✅ تم حساب السعر: $' + Math.round(currentPrice);
            }
        };
        
        // ربط أحداث إعادة التعيين
        ui.resetButton.onClick = function() {
            var result = confirm('هل أنت متأكد من إعادة تعيين جميع البيانات؟');
            if (result) {
                resetFormEnhanced(ui);
            }
        };
        
        // ربط أحداث العملة
        ui.currencyButton.onClick = function() {
            if (typeof showCurrencyManager === 'function') {
                var result = showCurrencyManager();
                if (result.selected) {
                    currentCurrency = result.currency;
                    ui.statusText.text = '💱 تم تغيير العملة إلى ' + AdvancedConfig.currencies[currentCurrency].nameAr;
                    // إعادة حساب السعر إذا كان موجود
                    if (currentPrice > 0) {
                        calculatePriceEnhanced(ui);
                    }
                }
            } else {
                alert('ميزة إدارة العملات غير متاحة');
            }
        };


        
        // ربط أحداث التحليل التلقائي
        ui.analyzeButton.onClick = function() {
            if (typeof analyzeDesignFile === 'function') {
                var analysis = analyzeDesignFile();
                if (analysis) {
                    var result = showAnalysisResults(analysis);
                    if (result.apply) {
                        ui.timeInput.text = Math.round(result.hours).toString();
                        ui.timeSlider.value = result.hours;
                        ui.analysisText.text = '✅ تم تطبيق نتائج التحليل: ' + analysis.layers + ' طبقة، ' + Math.round(result.hours) + ' ساعة';
                        ui.statusText.text = '🧠 تم تحليل الملف وتطبيق النتائج';
                    }
                }
            } else {
                alert('ميزة التحليل التلقائي غير متاحة');
            }
        };
        

        
        // ربط أحداث السجل
        ui.historyButton.onClick = function() {
            if (typeof showProjectHistory === 'function') {
                showProjectHistory();
            } else {
                alert('ميزة سجل المشاريع غير متاحة');
            }
        };
        
        // ربط أحداث الملاحظات
        ui.notesButton.onClick = function() {
            if (typeof manageClientNotes === 'function') {
                var result = manageClientNotes();
                if (result.saved) {
                    // حفظ البيانات في المتغيرات العامة
                    clientName = result.clientName || '';
                    clientNotes = result.notes || '';
                    ui.statusText.text = '💬 تم حفظ ملاحظات العميل: ' + clientName;
                }
            } else {
                // دالة بديلة بسيطة لإدخال الملاحظات
                var result = showSimpleNotesDialog();
                if (result.saved) {
                    ui.statusText.text = '💬 تم حفظ ملاحظات العميل: ' + (clientName || 'غير محدد');
                }
            }
        };
        





        
        // ربط أحداث التصدير
        ui.exportArabicBtn.onClick = function() {
            if (currentPrice > 0) {
                saveEstimateWithLanguage(ui, currentPrice, 'arabic');
            } else {
                alert('⚠️ يرجى حساب السعر أولاً قبل التصدير');
            }
        };

        ui.exportEnglishBtn.onClick = function() {
            if (currentPrice > 0) {
                saveEstimateWithLanguage(ui, currentPrice, 'english');
            } else {
                alert('⚠️ Please calculate the price first before exporting');
            }
        };

        ui.exportBilingualBtn.onClick = function() {
            if (currentPrice > 0) {
                saveEstimateWithLanguage(ui, currentPrice, 'bilingual');
            } else {
                alert('⚠️ يرجى حساب السعر أولاً قبل التصدير / Please calculate the price first before exporting');
            }
        };

        // ربط أحداث الإغلاق
        ui.closeButton.onClick = function() {
            ui.dialog.close();
        };

        // رسالة ترحيب
        ui.statusText.text = '🎉 مرحباً بك في النسخة الاحترافية!';
        
        // عرض الواجهة
        ui.dialog.show();
        
    } catch (error) {
        alert('❌ خطأ في تشغيل السكريبت الاحترافي: ' + error.message);
    }
}

// ═══════════════════════════════════════════════════════════════════════════════
// تشغيل السكريبت - RUN SCRIPT
// ═══════════════════════════════════════════════════════════════════════════════

// رسالة ترحيب محسنة
alert('🎉 مرحباً بك في حاسبة أسعار التصميم الاحترافية v2.0\n\n' +
      '✨ الميزات المتاحة:\n' +
      '🧠 تحليل تلقائي للملفات\n' +
      '🌍 دعم عملات متعددة\n' +
      '⚙️ تخصيص سعر الساعة المتقدم\n' +
      '🔧 حساب دقيق للتعقيد والتعديلات\n' +
      '💬 ملاحظات العميل\n' +
      '🧾 سجل المشاريع\n' +
      '📁 تصدير متعدد اللغات (عربي/إنجليزي/ثنائي)\n' +
      '📊 تقارير مفصلة للتسعير\n\n' +
      'استمتع بالحساب والتصدير! 🚀');

// تشغيل السكريبت الاحترافي
mainEnhanced();

/*
═══════════════════════════════════════════════════════════════════════════════
                                 نهاية السكريبت
                                 END OF SCRIPT
═══════════════════════════════════════════════════════════════════════════════
*/
