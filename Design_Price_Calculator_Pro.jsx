/*
═══════════════════════════════════════════════════════════════════════════════
                    حاسبة أسعار التصميم الاحترافية - الإصدار المتقدم
                   Professional Design Price Calculator - Advanced Version
═══════════════════════════════════════════════════════════════════════════════

الإصدار: 2.0.0
التاريخ: 18 ديسمبر 2024
المطور: Design Price Calculator Team
الترخيص: MIT License

الميزات الجديدة:
✅ تحليل تلقائي للملفات
✅ تخصيص سعر الساعة
✅ دعم عملات متعددة
✅ توليد عرض سعر احترافي
✅ حفظ واسترجاع عروض قديمة
✅ مؤقت لتتبع الوقت
✅ نظام تعديل مرن
✅ إضافة ملاحظات للعميل
✅ ثيم داكن + واجهة احترافية
✅ تصدير متعدد اللغات محسن

═══════════════════════════════════════════════════════════════════════════════
*/

// التحقق من توفر فوتوشوب
if (typeof app === 'undefined' || app.name !== 'Adobe Photoshop') {
    alert('هذا السكريبت يعمل فقط في برنامج Adobe Photoshop');
    throw new Error('Adobe Photoshop required');
}

// إضافة دعم Object.keys للإصدارات القديمة من ExtendScript
if (!Object.keys) {
    Object.keys = function(obj) {
        var keys = [];
        for (var key in obj) {
            if (obj.hasOwnProperty(key)) {
                keys.push(key);
            }
        }
        return keys;
    };
}

// تحميل الملفات المساعدة
try {
    var scriptPath = $.fileName.replace(/[^\/\\]*$/, '');
    
    // تحميل الإعدادات الأساسية
    var configFile = new File(scriptPath + 'pricing_config.jsx');
    if (configFile.exists) {
        $.evalFile(configFile);
    }
    
    // تحميل الميزات المتقدمة
    var advancedFile = new File(scriptPath + 'advanced_features.jsx');
    if (advancedFile.exists) {
        $.evalFile(advancedFile);
    }
    
    // تحميل الواجهة المحسنة
    var uiFile = new File(scriptPath + 'enhanced_ui.jsx');
    if (uiFile.exists) {
        $.evalFile(uiFile);
    }
    
    // تحميل وظائف التصدير
    var exportFile = new File(scriptPath + 'export_functions.jsx');
    if (exportFile.exists) {
        $.evalFile(exportFile);
    }

    // تحميل مولد PDF
    var pdfFile = new File(scriptPath + 'pdf_generator.jsx');
    if (pdfFile.exists) {
        $.evalFile(pdfFile);
    }

    // تحميل مولد PDF
    var pdfFile = new File(scriptPath + 'pdf_generator.jsx');
    if (pdfFile.exists) {
        $.evalFile(pdfFile);
    }
    
} catch (e) {
    // في حالة عدم وجود الملفات الإضافية، استخدم الإعدادات المدمجة
    alert('⚠️ بعض الملفات المساعدة غير موجودة. سيتم استخدام الإعدادات الافتراضية.');
}

// متغيرات عامة
var currentPrice = 0;
var currentCurrency = 'USD';
var currentTheme = 'light';

// ═══════════════════════════════════════════════════════════════════════════════
// الدوال الرئيسية المحسنة - ENHANCED MAIN FUNCTIONS
// ═══════════════════════════════════════════════════════════════════════════════

// دالة حساب السعر المحسنة مع دعم العملات
function calculatePriceEnhanced(ui) {
    try {
        // التحقق من صحة البيانات
        if (!ui.designTypeDropdown.selection) {
            ui.statusText.text = '❌ يرجى اختيار نوع التصميم';
            return 0;
        }
        
        ui.statusText.text = '🔄 جاري الحساب...';
        
        // الحصول على القيم من الواجهة
        var designTypeIndex = ui.designTypeDropdown.selection.index;
        var designTypeKeys = Object.keys(PricingConfig.designTypes);
        var designType = PricingConfig.designTypes[designTypeKeys[designTypeIndex]];
        
        var hours = parseFloat(ui.timeInput.text) || 0;
        if (hours <= 0) {
            ui.statusText.text = '❌ يرجى إدخال عدد ساعات صحيح';
            return 0;
        }
        
        var complexityLevel = Math.round(ui.complexitySlider.value);
        var revisions = Math.max(0, parseInt(ui.revisionsInput.text) || 0);
        
        // تحديد نوع الاستخدام
        var usageType = 'personal';
        if (ui.usageSmallBusiness.value) usageType = 'small_business';
        else if (ui.usageCommercial.value) usageType = 'commercial';
        else if (ui.usageEnterprise.value) usageType = 'enterprise';
        
        // تحديد سرعة التسليم
        var deliveryType = 'normal';
        if (ui.deliveryFast.value) deliveryType = 'fast';
        else if (ui.deliveryUrgent.value) deliveryType = 'urgent';
        else if (ui.deliveryExpress.value) deliveryType = 'express';
        
        // حساب السعر الأساسي
        var basePrice = designType.basePrice * hours;
        
        // تطبيق المعاملات
        var complexityKeys = Object.keys(PricingConfig.complexityMultipliers);
        var complexityData = PricingConfig.complexityMultipliers[complexityKeys[complexityLevel - 1]];
        var usageData = PricingConfig.usageMultipliers[usageType];
        var deliveryData = PricingConfig.deliveryMultipliers[deliveryType];
        
        // حساب تكلفة التعديلات
        var freeRevisions = 2;
        var paidRevisions = Math.max(0, revisions - freeRevisions);
        var revisionsCost = paidRevisions * PricingConfig.revisionCost;
        
        // حساب السعر النهائي
        var priceBeforeRevisions = basePrice * complexityData.multiplier * usageData.multiplier * deliveryData.multiplier;
        var finalPrice = priceBeforeRevisions + revisionsCost;
        
        // تطبيق الحدود
        finalPrice = Math.max(PricingConfig.settings.minProjectValue, finalPrice);
        finalPrice = Math.min(PricingConfig.settings.maxProjectValue, finalPrice);
        
        // تحويل العملة
        if (typeof AdvancedConfig !== 'undefined' && AdvancedConfig.currencies[currentCurrency]) {
            finalPrice = finalPrice * AdvancedConfig.currencies[currentCurrency].rate;
            ui.currencySymbol.text = AdvancedConfig.currencies[currentCurrency].symbol;
        }
        
        // تحديث العرض
        ui.priceDisplay.text = Math.round(finalPrice).toString();
        ui.statusText.text = '✅ تم حساب السعر بنجاح';
        
        // عرض التفاصيل
        var details = '📊 تفاصيل حساب السعر:\n\n';
        details += '🎨 نوع التصميم: ' + designType.name + ' ($' + designType.basePrice + '/ساعة)\n';
        details += '⏰ عدد الساعات: ' + hours + ' ساعة\n';
        details += '💵 السعر الأساسي: $' + Math.round(basePrice) + '\n\n';
        details += '🔧 معامل التعقيد: ' + complexityData.name + ' (x' + complexityData.multiplier + ')\n';
        details += '👥 معامل الاستخدام: ' + usageData.name + ' (x' + usageData.multiplier + ')\n';
        details += '🚀 معامل التسليم: ' + deliveryData.name + ' (x' + deliveryData.multiplier + ')\n\n';
        details += '💰 السعر بعد المعاملات: $' + Math.round(priceBeforeRevisions) + '\n';
        
        if (paidRevisions > 0) {
            details += '✏️ تعديلات إضافية: ' + paidRevisions + ' × $' + PricingConfig.revisionCost + ' = $' + revisionsCost + '\n';
        } else {
            details += '✏️ التعديلات: ' + revisions + ' (مشمولة مجاناً)\n';
        }
        
        details += '\n🎯 السعر النهائي: ' + ui.currencySymbol.text + Math.round(finalPrice);
        
        if (currentCurrency !== 'USD') {
            details += '\n💱 العملة: ' + AdvancedConfig.currencies[currentCurrency].nameAr;
        }
        
        ui.detailsDisplay.text = details;
        
        return finalPrice;
        
    } catch (error) {
        ui.statusText.text = '❌ خطأ في الحساب';
        alert('خطأ في حساب السعر: ' + error.message);
        return 0;
    }
}

// دالة إعادة تعيين النموذج المحسنة
function resetFormEnhanced(ui) {
    try {
        ui.statusText.text = '🔄 جاري إعادة التعيين...';
        
        // إعادة تعيين القيم
        ui.designTypeDropdown.selection = 0;
        ui.timeInput.text = '5';
        ui.timeSlider.value = 5;
        ui.complexitySlider.value = 1;
        ui.complexitySlider.onChanging();
        ui.revisionsInput.text = '2';
        
        // إعادة تعيين أزرار الاختيار
        ui.usagePersonal.value = true;
        ui.usageSmallBusiness.value = false;
        ui.usageCommercial.value = false;
        ui.usageEnterprise.value = false;
        
        ui.deliveryNormal.value = true;
        ui.deliveryFast.value = false;
        ui.deliveryUrgent.value = false;
        ui.deliveryExpress.value = false;
        
        // مسح النتائج
        ui.priceDisplay.text = '0';
        ui.detailsDisplay.text = '';
        ui.statusText.text = '✨ تم إعادة التعيين - جاهز للحساب';
        
        currentPrice = 0;
        
    } catch (error) {
        ui.statusText.text = '❌ خطأ في إعادة التعيين';
        alert('خطأ في إعادة التعيين: ' + error.message);
    }
}

// دالة تبديل الثيم
function toggleTheme(ui) {
    currentTheme = currentTheme === 'light' ? 'dark' : 'light';
    ui.themeButton.text = currentTheme === 'dark' ? '☀️ ثيم فاتح' : '🌙 ثيم داكن';
    
    // تطبيق الثيم (محدود في ExtendScript)
    try {
        if (typeof applyTheme === 'function') {
            applyTheme(ui.dialog, currentTheme);
        }
    } catch (e) {
        // تجاهل أخطاء تطبيق الثيم
    }
    
    ui.statusText.text = '🎨 تم تغيير الثيم إلى ' + (currentTheme === 'dark' ? 'داكن' : 'فاتح');
}

// ═══════════════════════════════════════════════════════════════════════════════
// الدالة الرئيسية المحسنة - ENHANCED MAIN FUNCTION
// ═══════════════════════════════════════════════════════════════════════════════

function mainEnhanced() {
    try {
        // إنشاء الواجهة المحسنة
        var ui;
        if (typeof createEnhancedUI === 'function') {
            ui = createEnhancedUI();
        } else {
            alert('❌ ملف الواجهة المحسنة غير موجود. سيتم استخدام الواجهة الأساسية.');
            return;
        }
        
        // ربط أحداث الحساب
        ui.calculateButton.onClick = function() {
            currentPrice = calculatePriceEnhanced(ui);
        };
        
        // ربط أحداث إعادة التعيين
        ui.resetButton.onClick = function() {
            var result = confirm('هل أنت متأكد من إعادة تعيين جميع البيانات؟');
            if (result) {
                resetFormEnhanced(ui);
            }
        };
        
        // ربط أحداث الثيم
        ui.themeButton.onClick = function() {
            toggleTheme(ui);
        };
        
        // ربط أحداث العملة
        ui.currencyButton.onClick = function() {
            if (typeof showCurrencyManager === 'function') {
                var result = showCurrencyManager();
                if (result.selected) {
                    currentCurrency = result.currency;
                    ui.statusText.text = '💱 تم تغيير العملة إلى ' + AdvancedConfig.currencies[currentCurrency].nameAr;
                    // إعادة حساب السعر إذا كان موجود
                    if (currentPrice > 0) {
                        calculatePriceEnhanced(ui);
                    }
                }
            } else {
                alert('ميزة إدارة العملات غير متاحة');
            }
        };

        // ربط أحداث الإعدادات
        ui.settingsButton.onClick = function() {
            if (typeof showAdvancedPriceCustomizer === 'function') {
                var result = showAdvancedPriceCustomizer();
                if (result.saved) {
                    ui.statusText.text = '⚙️ تم تحديث إعدادات التسعير';
                    // إعادة حساب السعر إذا كان موجود
                    if (currentPrice > 0) {
                        calculatePriceEnhanced(ui);
                    }
                }
            } else {
                alert('ميزة الإعدادات المتقدمة غير متاحة - تأكد من وجود ملف price_customizer.jsx');
            }
        };
        
        // ربط أحداث التحليل التلقائي
        ui.analyzeButton.onClick = function() {
            if (typeof analyzeDesignFile === 'function') {
                var analysis = analyzeDesignFile();
                if (analysis) {
                    var result = showAnalysisResults(analysis);
                    if (result.apply) {
                        ui.timeInput.text = Math.round(result.hours).toString();
                        ui.timeSlider.value = result.hours;
                        ui.analysisText.text = '✅ تم تطبيق نتائج التحليل: ' + analysis.layers + ' طبقة، ' + Math.round(result.hours) + ' ساعة';
                        ui.statusText.text = '🧠 تم تحليل الملف وتطبيق النتائج';
                    }
                }
            } else {
                alert('ميزة التحليل التلقائي غير متاحة');
            }
        };
        
        // ربط أحداث المؤقت
        ui.timerButton.onClick = function() {
            if (typeof createTimer === 'function') {
                createTimer();
            } else {
                alert('ميزة المؤقت غير متاحة');
            }
        };
        
        // ربط أحداث السجل
        ui.historyButton.onClick = function() {
            if (typeof showProjectHistory === 'function') {
                showProjectHistory();
            } else {
                alert('ميزة سجل المشاريع غير متاحة');
            }
        };
        
        // ربط أحداث الملاحظات
        ui.notesButton.onClick = function() {
            if (typeof manageClientNotes === 'function') {
                var result = manageClientNotes();
                if (result.saved) {
                    ui.statusText.text = '💬 تم حفظ ملاحظات العميل: ' + result.clientName;
                }
            } else {
                alert('ميزة ملاحظات العميل غير متاحة');
            }
        };
        
        // ربط أحداث التصدير
        ui.exportArabicBtn.onClick = function() {
            if (currentPrice > 0) {
                if (typeof saveEstimateWithLanguage === 'function') {
                    saveEstimateWithLanguage(ui, currentPrice, 'arabic');
                } else {
                    alert('وظائف التصدير المتقدمة غير متاحة');
                }
            } else {
                alert('⚠️ يرجى حساب السعر أولاً');
            }
        };
        
        ui.exportEnglishBtn.onClick = function() {
            if (currentPrice > 0) {
                if (typeof saveEstimateWithLanguage === 'function') {
                    saveEstimateWithLanguage(ui, currentPrice, 'english');
                } else {
                    alert('وظائف التصدير المتقدمة غير متاحة');
                }
            } else {
                alert('⚠️ يرجى حساب السعر أولاً');
            }
        };
        
        ui.exportBilingualBtn.onClick = function() {
            if (currentPrice > 0) {
                if (typeof saveEstimateWithLanguage === 'function') {
                    saveEstimateWithLanguage(ui, currentPrice, 'bilingual');
                } else {
                    alert('وظائف التصدير المتقدمة غير متاحة');
                }
            } else {
                alert('⚠️ يرجى حساب السعر أولاً');
            }
        };
        
        ui.exportCSVBtn.onClick = function() {
            if (currentPrice > 0) {
                if (typeof saveAsCSVWithLanguage === 'function') {
                    saveAsCSVWithLanguage(ui, currentPrice, 'arabic');
                } else {
                    alert('وظائف التصدير المتقدمة غير متاحة');
                }
            } else {
                alert('⚠️ يرجى حساب السعر أولاً');
            }
        };
        
        ui.exportPDFBtn.onClick = function() {
            if (currentPrice > 0) {
                if (typeof exportToPDF === 'function') {
                    exportToPDF(ui, currentPrice);
                } else {
                    alert('📄 ميزة تصدير PDF غير متاحة - تأكد من وجود ملف pdf_generator.jsx');
                }
            } else {
                alert('⚠️ يرجى حساب السعر أولاً');
            }
        };
        
        // ربط أحداث الإغلاق
        ui.closeButton.onClick = function() {
            ui.dialog.close();
        };
        
        // رسالة ترحيب
        ui.statusText.text = '🎉 مرحباً بك في النسخة الاحترافية!';
        
        // عرض الواجهة
        ui.dialog.show();
        
    } catch (error) {
        alert('❌ خطأ في تشغيل السكريبت الاحترافي: ' + error.message);
    }
}

// ═══════════════════════════════════════════════════════════════════════════════
// تشغيل السكريبت - RUN SCRIPT
// ═══════════════════════════════════════════════════════════════════════════════

// رسالة ترحيب محسنة
alert('🎉 مرحباً بك في حاسبة أسعار التصميم الاحترافية v2.0\n\n' +
      '✨ الميزات الجديدة:\n' +
      '🧠 تحليل تلقائي للملفات\n' +
      '🌍 دعم عملات متعددة\n' +
      '⏱️ مؤقت تتبع الوقت\n' +
      '🌙 ثيم داكن احترافي\n' +
      '📊 تصدير محسن متعدد اللغات\n' +
      '💬 ملاحظات العميل\n' +
      '🧾 سجل المشاريع\n\n' +
      'استمتع بالتجربة الاحترافية! 🚀');

// تشغيل السكريبت الاحترافي
mainEnhanced();

/*
═══════════════════════════════════════════════════════════════════════════════
                                 نهاية السكريبت
                                 END OF SCRIPT
═══════════════════════════════════════════════════════════════════════════════
*/
