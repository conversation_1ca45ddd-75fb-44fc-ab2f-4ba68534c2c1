# سجل التغييرات - Changelog

جميع التغييرات المهمة في مشروع حاسبة أسعار التصميم موثقة في هذا الملف.

## [الإصدار 1.1.0] - 2024-12-18

### ✨ الميزات الجديدة
- **🌐 التصدير متعدد اللغات**: دعم التصدير بالعربية والإنجليزية وثنائي اللغة
- **🔧 إصلاحات التوافق**: حل مشكلة "Object.keys is not a function" في الإصدارات القديمة
- **📁 واجهة اختيار اللغة**: واجهة سهلة لاختيار لغة التصدير
- **📊 CSV متعدد اللغات**: تصدير بيانات CSV بلغات مختلفة
- **🔄 JSON محسن**: ملفات JSON تحتوي على النصوص بكلا اللغتين

### 🔧 التحسينات
- تحسين دعم ExtendScript للإصدارات القديمة
- إضافة ملف إصلاحات التوافق المنفصل
- تحسين رسائل الخطأ متعددة اللغات
- تحديث التوثيق مع أمثلة متعددة اللغات

## [الإصدار 1.0.0] - 2024-12-18

### ✨ الميزات الجديدة
- **واجهة مستخدم شاملة**: واجهة سهلة الاستخدام مع جميع العناصر المطلوبة
- **حساب الأسعار الذكي**: خوارزمية متقدمة لحساب الأسعار بناءً على معايير متعددة
- **أنواع تصميم متنوعة**: دعم 10 أنواع مختلفة من التصميم
- **معاملات مرنة**: معاملات قابلة للتخصيص للتعقيد والاستخدام والتسليم
- **نظام التعديلات**: حساب تكلفة التعديلات مع تعديلات مجانية
- **تصدير متعدد الصيغ**: حفظ التقديرات بصيغ مختلفة (TXT, CSV, JSON)
- **عروض أسعار احترافية**: إنشاء عروض أسعار جاهزة للعملاء
- **نظام التحقق**: التحقق من صحة البيانات المدخلة
- **واجهة عربية**: دعم كامل للغة العربية

### 🎨 أنواع التصميم المدعومة
- لوجو ($50/ساعة)
- سوشيال ميديا ($25/ساعة)
- هوية بصرية ($75/ساعة)
- بوستر ($40/ساعة)
- UI/UX ($60/ساعة)
- كارت شخصي ($30/ساعة)
- بروشور ($45/ساعة)
- تصميم عبوات ($80/ساعة)
- بانر ويب ($35/ساعة)
- إنفوجرافيك ($55/ساعة)

### 🔧 معاملات التسعير
#### درجة التعقيد:
- بسيط: x1.0
- متوسط: x1.5
- معقد: x2.0
- معقد جداً: x2.5

#### نوع الاستخدام:
- شخصي: x1.0
- مشروع صغير: x1.4
- تجاري: x1.8
- مؤسسي: x2.5

#### سرعة التسليم:
- عادي: x1.0
- سريع: x1.3
- مستعجل: x1.5
- فوري: x2.0

### 💰 التكاليف الإضافية
- تكلفة التعديل: $15
- ساعة استشارة: $40
- الملفات المصدرية: $20
- العمل المستعجل: $50
- العمل في نهاية الأسبوع: $30

### 📁 ملفات المشروع
- `Design_Price_Calculator.jsx` - الملف الرئيسي
- `pricing_config.jsx` - إعدادات التسعير
- `export_functions.jsx` - وظائف التصدير المتقدمة
- `price_customizer.jsx` - أداة تخصيص الأسعار
- `test_script.jsx` - سكريبت الاختبار
- `README.md` - دليل المستخدم
- `INSTALLATION_GUIDE.md` - دليل التثبيت
- `EXAMPLES.md` - أمثلة وحالات الاستخدام

### 🧪 الاختبارات
- اختبار الإعدادات الأساسية
- اختبار حساب الأسعار
- اختبار واجهة المستخدم
- اختبار وظائف التصدير
- اختبار التحقق من صحة البيانات

### 🔒 متطلبات النظام
- Adobe Photoshop CS6 أو أحدث
- نظام التشغيل: Windows أو macOS
- ذاكرة: 512 MB RAM كحد أدنى
- مساحة القرص: 10 MB

### 📝 الملاحظات
- هذا الإصدار الأول من السكريبت
- تم اختباره على Photoshop CC 2020 و 2021
- يدعم اللغة العربية والإنجليزية
- قابل للتخصيص والتطوير

### 🐛 المشاكل المعروفة
- لا توجد مشاكل معروفة في هذا الإصدار

### 🔮 الميزات المخططة للإصدارات القادمة
- دعم العملات المختلفة
- حفظ وتحميل الإعدادات المخصصة
- تكامل مع قواعد البيانات
- تقارير إحصائية متقدمة
- دعم القوالب المخصصة
- تصدير PDF مباشر
- واجهة ويب مصاحبة
- تطبيق موبايل

---

## تنسيق سجل التغييرات

هذا السجل يتبع [Keep a Changelog](https://keepachangelog.com/en/1.0.0/) ويلتزم بـ [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

### أنواع التغييرات
- `Added` للميزات الجديدة
- `Changed` للتغييرات في الوظائف الموجودة
- `Deprecated` للميزات التي ستُزال قريباً
- `Removed` للميزات المُزالة
- `Fixed` لإصلاح الأخطاء
- `Security` لإصلاحات الأمان

---

**ملاحظة**: التواريخ بصيغة YYYY-MM-DD
