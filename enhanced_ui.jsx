/*
واجهة المستخدم المحسنة - Enhanced UI
واجهة احترافية مع ثيم داكن ومفاتيح متقدمة
*/

// إضافة دعم Object.keys للإصدارات القديمة من ExtendScript
if (!Object.keys) {
    Object.keys = function(obj) {
        var keys = [];
        for (var key in obj) {
            if (obj.hasOwnProperty(key)) {
                keys.push(key);
            }
        }
        return keys;
    };
}

// إعدادات الثيم
var ThemeConfig = {
    light: {
        background: [0.95, 0.95, 0.95],
        panel: [0.9, 0.9, 0.9],
        text: [0.1, 0.1, 0.1],
        accent: [0.2, 0.4, 0.8],
        button: [0.85, 0.85, 0.85],
        buttonHover: [0.75, 0.75, 0.75]
    },
    dark: {
        background: [0.15, 0.15, 0.15],
        panel: [0.2, 0.2, 0.2],
        text: [0.9, 0.9, 0.9],
        accent: [0.3, 0.6, 1.0],
        button: [0.3, 0.3, 0.3],
        buttonHover: [0.4, 0.4, 0.4]
    }
};

var currentTheme = 'light';

// دالة تطبيق الثيم
function applyTheme(element, theme) {
    try {
        var colors = ThemeConfig[theme];
        
        if (element.graphics) {
            element.graphics.backgroundColor = element.graphics.newBrush(element.graphics.BrushType.SOLID_COLOR, colors.background);
            element.graphics.foregroundColor = element.graphics.newPen(element.graphics.PenType.SOLID_COLOR, colors.text, 1);
        }
    } catch (e) {
        // تجاهل الأخطاء في تطبيق الثيم
    }
}

// دالة إنشاء الواجهة المحسنة
function createEnhancedUI() {
    var dialog = new Window('dialog', '💎 حاسبة أسعار التصميم الاحترافية - Pro Design Price Calculator');
    dialog.orientation = 'column';
    dialog.alignChildren = 'fill';
    dialog.spacing = 10;
    dialog.margins = 20;
    dialog.preferredSize.width = 700;
    dialog.preferredSize.height = 800;
    
    // شريط علوي مع الشعار والإعدادات
    var headerPanel = dialog.add('panel');
    headerPanel.orientation = 'row';
    headerPanel.alignChildren = 'center';
    headerPanel.margins = 15;
    headerPanel.preferredSize.height = 80;
    
    // مجموعة الشعار والعنوان
    var logoGroup = headerPanel.add('group');
    logoGroup.orientation = 'row';
    logoGroup.alignChildren = 'center';
    
    var logoText = logoGroup.add('statictext', undefined, '💎');
    logoText.graphics.font = ScriptUI.newFont(logoText.graphics.font.name, ScriptUI.FontStyle.BOLD, 32);
    
    var titleGroup = logoGroup.add('group');
    titleGroup.orientation = 'column';
    titleGroup.alignChildren = 'left';
    
    var mainTitle = titleGroup.add('statictext', undefined, 'حاسبة أسعار التصميم الاحترافية');
    mainTitle.graphics.font = ScriptUI.newFont(mainTitle.graphics.font.name, ScriptUI.FontStyle.BOLD, 16);
    
    var subtitle = titleGroup.add('statictext', undefined, 'Professional Design Price Calculator v2.0');
    subtitle.graphics.font = ScriptUI.newFont(subtitle.graphics.font.name, ScriptUI.FontStyle.ITALIC, 11);
    
    // مجموعة الإعدادات السريعة
    var quickSettings = headerPanel.add('group');
    quickSettings.orientation = 'row';
    quickSettings.alignChildren = 'center';

    var currencyButton = quickSettings.add('button', undefined, '💱 العملة');
    currencyButton.preferredSize.width = 100;
    
    // شريط الأدوات
    var toolbarPanel = dialog.add('panel');
    toolbarPanel.orientation = 'row';
    toolbarPanel.alignChildren = 'center';
    toolbarPanel.margins = 10;
    
    var analyzeButton = toolbarPanel.add('button', undefined, '🧠 تحليل تلقائي');
    var historyButton = toolbarPanel.add('button', undefined, '🧾 السجل');
    var notesButton = toolbarPanel.add('button', undefined, '💬 ملاحظات');

    // تحسين أحجام الأزرار
    analyzeButton.preferredSize.width = 120;
    historyButton.preferredSize.width = 80;
    notesButton.preferredSize.width = 100;
    
    // المحتوى الرئيسي في تبويبات
    var tabPanel = dialog.add('tabbedpanel');
    tabPanel.alignChildren = 'fill';
    tabPanel.preferredSize.height = 500;
    
    // تبويب التسعير الأساسي
    var basicTab = tabPanel.add('tab', undefined, '💰 التسعير الأساسي');
    basicTab.orientation = 'column';
    basicTab.alignChildren = 'fill';
    basicTab.spacing = 15;
    
    // مجموعة نوع التصميم المحسنة
    var designTypeGroup = basicTab.add('panel', undefined, '🎨 نوع التصميم والتحليل');
    designTypeGroup.orientation = 'column';
    designTypeGroup.alignChildren = 'fill';
    designTypeGroup.margins = 15;
    
    var designTypeRow = designTypeGroup.add('group');
    designTypeRow.orientation = 'row';
    designTypeRow.alignChildren = 'left';
    
    var designTypeLabel = designTypeRow.add('statictext', undefined, 'نوع التصميم:');
    designTypeLabel.preferredSize.width = 100;
    
    var designTypeDropdown = designTypeRow.add('dropdownlist');
    designTypeDropdown.preferredSize.width = 200;
    
    var autoAnalyzeBtn = designTypeRow.add('button', undefined, '🔍 تحليل');
    autoAnalyzeBtn.preferredSize.width = 80;
    
    // إضافة أنواع التصميم
    for (var key in PricingConfig.designTypes) {
        var item = PricingConfig.designTypes[key];
        designTypeDropdown.add('item', item.name + ' ($' + item.basePrice + '/ساعة)');
    }
    designTypeDropdown.selection = 0;
    
    // معلومات التحليل
    var analysisInfo = designTypeGroup.add('group');
    analysisInfo.orientation = 'column';
    analysisInfo.alignChildren = 'fill';
    
    var analysisText = analysisInfo.add('statictext', undefined, '💡 اضغط "تحليل" لتحليل الملف المفتوح تلقائياً');
    analysisText.graphics.font = ScriptUI.newFont(analysisText.graphics.font.name, ScriptUI.FontStyle.ITALIC, 10);
    
    // مجموعة الوقت والتعقيد
    var timeComplexityGroup = basicTab.add('panel', undefined, '⏰ الوقت والتعقيد');
    timeComplexityGroup.orientation = 'row';
    timeComplexityGroup.alignChildren = 'fill';
    timeComplexityGroup.margins = 15;
    
    // عمود الوقت
    var timeColumn = timeComplexityGroup.add('group');
    timeColumn.orientation = 'column';
    timeColumn.alignChildren = 'fill';
    
    timeColumn.add('statictext', undefined, 'عدد الساعات:');
    var timeInput = timeColumn.add('edittext', undefined, '5');
    timeInput.preferredSize.width = 100;
    timeInput.characters = 10;
    
    var timeSlider = timeColumn.add('slider', undefined, 5, 1, 50);
    timeSlider.preferredSize.width = 150;
    
    // ربط المنزلق بالحقل النصي
    timeSlider.onChanging = function() {
        timeInput.text = Math.round(this.value).toString();
    };
    
    timeInput.onChanging = function() {
        var value = parseFloat(this.text) || 5;
        timeSlider.value = Math.max(1, Math.min(50, value));
    };
    
    // عمود التعقيد
    var complexityColumn = timeComplexityGroup.add('group');
    complexityColumn.orientation = 'column';
    complexityColumn.alignChildren = 'fill';
    
    complexityColumn.add('statictext', undefined, 'درجة التعقيد:');
    
    var complexitySlider = complexityColumn.add('slider', undefined, 1, 1, 4);
    complexitySlider.preferredSize.width = 200;
    
    var complexityValue = complexityColumn.add('statictext', undefined, 'بسيط (x1.0)');
    complexityValue.preferredSize.width = 150;
    
    // تحديث نص التعقيد
    complexitySlider.onChanging = function() {
        var value = Math.round(this.value);
        var complexityKeys = Object.keys(PricingConfig.complexityMultipliers);
        var complexity = PricingConfig.complexityMultipliers[complexityKeys[value - 1]];
        complexityValue.text = complexity.name + ' (x' + complexity.multiplier + ')';
    };
    complexitySlider.onChanging();
    
    // مجموعة التعديلات والاستخدام
    var revisionsUsageGroup = basicTab.add('panel', undefined, '✏️ التعديلات والاستخدام');
    revisionsUsageGroup.orientation = 'row';
    revisionsUsageGroup.alignChildren = 'fill';
    revisionsUsageGroup.margins = 15;
    
    // عمود التعديلات
    var revisionsColumn = revisionsUsageGroup.add('group');
    revisionsColumn.orientation = 'column';
    revisionsColumn.alignChildren = 'fill';
    
    revisionsColumn.add('statictext', undefined, 'عدد التعديلات:');
    var revisionsInput = revisionsColumn.add('edittext', undefined, '2');
    revisionsInput.preferredSize.width = 80;
    
    var freeRevisionsText = revisionsColumn.add('statictext', undefined, '(أول 2 مجاناً)');
    freeRevisionsText.graphics.font = ScriptUI.newFont(freeRevisionsText.graphics.font.name, ScriptUI.FontStyle.ITALIC, 9);
    
    // عمود نوع الاستخدام
    var usageColumn = revisionsUsageGroup.add('group');
    usageColumn.orientation = 'column';
    usageColumn.alignChildren = 'fill';
    
    usageColumn.add('statictext', undefined, 'نوع الاستخدام:');
    
    var usagePersonal = usageColumn.add('radiobutton', undefined, 'شخصي (x1.0)');
    var usageSmallBusiness = usageColumn.add('radiobutton', undefined, 'مشروع صغير (x1.4)');
    var usageCommercial = usageColumn.add('radiobutton', undefined, 'تجاري (x1.8)');
    var usageEnterprise = usageColumn.add('radiobutton', undefined, 'مؤسسي (x2.5)');
    usagePersonal.value = true;
    
    // عمود سرعة التسليم
    var deliveryColumn = revisionsUsageGroup.add('group');
    deliveryColumn.orientation = 'column';
    deliveryColumn.alignChildren = 'fill';
    
    deliveryColumn.add('statictext', undefined, 'سرعة التسليم:');
    
    var deliveryNormal = deliveryColumn.add('radiobutton', undefined, 'عادي (x1.0)');
    var deliveryFast = deliveryColumn.add('radiobutton', undefined, 'سريع (x1.3)');
    var deliveryUrgent = deliveryColumn.add('radiobutton', undefined, 'مستعجل (x1.5)');
    var deliveryExpress = deliveryColumn.add('radiobutton', undefined, 'فوري (x2.0)');
    deliveryNormal.value = true;
    
    // تبويب النتائج والتصدير
    var resultsTab = tabPanel.add('tab', undefined, '📊 النتائج والتصدير');
    resultsTab.orientation = 'column';
    resultsTab.alignChildren = 'fill';
    
    // مجموعة النتائج
    var resultsGroup = resultsTab.add('panel', undefined, '💰 نتائج التسعير');
    resultsGroup.orientation = 'column';
    resultsGroup.alignChildren = 'fill';
    resultsGroup.margins = 15;
    
    // عرض السعر الرئيسي
    var priceDisplayGroup = resultsGroup.add('group');
    priceDisplayGroup.orientation = 'row';
    priceDisplayGroup.alignChildren = 'center';
    
    var currencySymbol = priceDisplayGroup.add('statictext', undefined, '$');
    currencySymbol.graphics.font = ScriptUI.newFont(currencySymbol.graphics.font.name, ScriptUI.FontStyle.BOLD, 24);
    
    var priceDisplay = priceDisplayGroup.add('statictext', undefined, '0');
    priceDisplay.graphics.font = ScriptUI.newFont(priceDisplay.graphics.font.name, ScriptUI.FontStyle.BOLD, 32);
    
    var priceLabel = priceDisplayGroup.add('statictext', undefined, 'السعر المقترح');
    priceLabel.graphics.font = ScriptUI.newFont(priceLabel.graphics.font.name, ScriptUI.FontStyle.REGULAR, 14);
    
    // تفاصيل الحساب
    var detailsDisplay = resultsGroup.add('edittext', undefined, '', {multiline: true, readonly: true});
    detailsDisplay.preferredSize.height = 150;
    detailsDisplay.preferredSize.width = 600;

    // مجموعة التصدير (خيارات اللغة فقط)
    var exportGroup = resultsTab.add('panel', undefined, '📁 تصدير التقدير');
    exportGroup.orientation = 'column';
    exportGroup.alignChildren = 'fill';
    exportGroup.margins = 15;

    var exportButtonsRow = exportGroup.add('group');
    exportButtonsRow.orientation = 'row';
    exportButtonsRow.alignChildren = 'center';
    exportButtonsRow.spacing = 10;

    var exportArabicBtn = exportButtonsRow.add('button', undefined, '🇸🇦 تصدير عربي');
    var exportEnglishBtn = exportButtonsRow.add('button', undefined, '🇺🇸 تصدير إنجليزي');
    var exportBilingualBtn = exportButtonsRow.add('button', undefined, '🌍 ثنائي اللغة');

    // تحسين أحجام الأزرار
    exportArabicBtn.preferredSize.width = 120;
    exportEnglishBtn.preferredSize.width = 120;
    exportBilingualBtn.preferredSize.width = 120;


    // شريط الحالة والأزرار الرئيسية
    var statusBar = dialog.add('panel');
    statusBar.orientation = 'row';
    statusBar.alignChildren = 'center';
    statusBar.margins = 10;
    
    var statusText = statusBar.add('statictext', undefined, '✨ جاهز للحساب');
    statusText.preferredSize.width = 200;
    
    var mainButtonsGroup = statusBar.add('group');
    mainButtonsGroup.orientation = 'row';
    mainButtonsGroup.alignChildren = 'center';
    mainButtonsGroup.spacing = 15;
    
    var calculateButton = mainButtonsGroup.add('button', undefined, '🧮 احسب السعر');
    calculateButton.preferredSize.width = 120;
    calculateButton.preferredSize.height = 40;
    
    var resetButton = mainButtonsGroup.add('button', undefined, '🔄 إعادة تعيين');
    resetButton.preferredSize.width = 120;
    resetButton.preferredSize.height = 40;
    
    var closeButton = mainButtonsGroup.add('button', undefined, '❌ إغلاق');
    closeButton.preferredSize.width = 100;
    closeButton.preferredSize.height = 40;
    
    // إرجاع كائن الواجهة
    return {
        dialog: dialog,
        // عناصر التحكم الأساسية
        designTypeDropdown: designTypeDropdown,
        timeInput: timeInput,
        timeSlider: timeSlider,
        complexitySlider: complexitySlider,
        complexityValue: complexityValue,
        revisionsInput: revisionsInput,
        // أزرار الاستخدام
        usagePersonal: usagePersonal,
        usageSmallBusiness: usageSmallBusiness,
        usageCommercial: usageCommercial,
        usageEnterprise: usageEnterprise,
        // أزرار التسليم
        deliveryNormal: deliveryNormal,
        deliveryFast: deliveryFast,
        deliveryUrgent: deliveryUrgent,
        deliveryExpress: deliveryExpress,
        // عرض النتائج
        priceDisplay: priceDisplay,
        currencySymbol: currencySymbol,
        detailsDisplay: detailsDisplay,
        statusText: statusText,
        // أزرار التحكم
        calculateButton: calculateButton,
        resetButton: resetButton,
        closeButton: closeButton,
        // أزرار الأدوات
        currencyButton: currencyButton,
        analyzeButton: analyzeButton,
        historyButton: historyButton,
        notesButton: notesButton,
        autoAnalyzeBtn: autoAnalyzeBtn,

        // أزرار التصدير
        exportArabicBtn: exportArabicBtn,
        exportEnglishBtn: exportEnglishBtn,
        exportBilingualBtn: exportBilingualBtn,

        // عناصر إضافية
        analysisText: analysisText
    };
}
