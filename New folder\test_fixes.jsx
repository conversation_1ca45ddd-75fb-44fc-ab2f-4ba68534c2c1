/*
ملف اختبار الإصلاحات - Test Fixes
اختبار سريع للتأكد من عمل الإصلاحات بشكل صحيح
*/

// اختبار وجود PricingConfig
function testPricingConfig() {
    alert('🧪 اختبار PricingConfig...');
    
    if (typeof PricingConfig === 'undefined') {
        alert('❌ PricingConfig غير موجود!');
        return false;
    }
    
    // اختبار تكلفة التعديل
    var revisionCost = 15; // قيمة افتراضية
    if (PricingConfig.additionalCosts && PricingConfig.additionalCosts.revisionCost) {
        revisionCost = PricingConfig.additionalCosts.revisionCost;
        alert('✅ تكلفة التعديل من additionalCosts: $' + revisionCost);
    } else if (PricingConfig.revisionCost) {
        revisionCost = PricingConfig.revisionCost;
        alert('✅ تكلفة التعديل من revisionCost: $' + revisionCost);
    } else {
        alert('⚠️ تكلفة التعديل غير موجودة، استخدام القيمة الافتراضية: $' + revisionCost);
    }
    
    return true;
}

// اختبار حساب التعديلات
function testRevisionCalculation() {
    alert('🧪 اختبار حساب التعديلات...');
    
    var revisions = 5; // 5 تعديلات
    var freeRevisions = 2; // 2 مجاني
    var paidRevisions = Math.max(0, revisions - freeRevisions); // 3 مدفوع
    
    var revisionCost = 15;
    if (typeof PricingConfig !== 'undefined') {
        if (PricingConfig.additionalCosts && PricingConfig.additionalCosts.revisionCost) {
            revisionCost = PricingConfig.additionalCosts.revisionCost;
        } else if (PricingConfig.revisionCost) {
            revisionCost = PricingConfig.revisionCost;
        }
    }
    
    var revisionsCost = paidRevisions * revisionCost;
    
    var result = 'نتيجة الاختبار:\n';
    result += 'إجمالي التعديلات: ' + revisions + '\n';
    result += 'التعديلات المجانية: ' + freeRevisions + '\n';
    result += 'التعديلات المدفوعة: ' + paidRevisions + '\n';
    result += 'تكلفة التعديل الواحد: $' + revisionCost + '\n';
    result += 'إجمالي تكلفة التعديلات: $' + revisionsCost;
    
    alert('✅ ' + result);
    
    // التحقق من عدم وجود NaN
    if (isNaN(revisionsCost)) {
        alert('❌ خطأ: النتيجة NaN!');
        return false;
    }
    
    return true;
}

// اختبار حساب السعر الكامل
function testFullPriceCalculation() {
    alert('🧪 اختبار حساب السعر الكامل...');
    
    try {
        // بيانات اختبار
        var hours = 5;
        var basePrice = 50;
        var complexityMultiplier = 1.5;
        var usageMultiplier = 1.8;
        var deliveryMultiplier = 1.0;
        var revisions = 4;
        var freeRevisions = 2;
        
        // حساب السعر الأساسي
        var totalBasePrice = basePrice * hours;
        
        // تطبيق المعاملات
        var priceBeforeRevisions = totalBasePrice * complexityMultiplier * usageMultiplier * deliveryMultiplier;
        
        // حساب التعديلات
        var paidRevisions = Math.max(0, revisions - freeRevisions);
        var revisionCost = 15;
        if (typeof PricingConfig !== 'undefined') {
            if (PricingConfig.additionalCosts && PricingConfig.additionalCosts.revisionCost) {
                revisionCost = PricingConfig.additionalCosts.revisionCost;
            }
        }
        var revisionsCost = paidRevisions * revisionCost;
        
        // السعر النهائي
        var finalPrice = priceBeforeRevisions + revisionsCost;
        
        var result = 'نتيجة حساب السعر:\n';
        result += 'السعر الأساسي: ' + hours + ' × $' + basePrice + ' = $' + totalBasePrice + '\n';
        result += 'بعد المعاملات: $' + Math.round(priceBeforeRevisions) + '\n';
        result += 'تكلفة التعديلات: ' + paidRevisions + ' × $' + revisionCost + ' = $' + revisionsCost + '\n';
        result += 'السعر النهائي: $' + Math.round(finalPrice);
        
        alert('✅ ' + result);
        
        // التحقق من عدم وجود NaN
        if (isNaN(finalPrice)) {
            alert('❌ خطأ: السعر النهائي NaN!');
            return false;
        }
        
        return true;
        
    } catch (error) {
        alert('❌ خطأ في الحساب: ' + error.message);
        return false;
    }
}

// تشغيل جميع الاختبارات
function runAllTests() {
    alert('🚀 بدء تشغيل اختبارات الإصلاحات...');
    
    var results = [];
    
    // اختبار 1: PricingConfig
    results.push(testPricingConfig());
    
    // اختبار 2: حساب التعديلات
    results.push(testRevisionCalculation());
    
    // اختبار 3: حساب السعر الكامل
    results.push(testFullPriceCalculation());
    
    // النتيجة النهائية
    var passed = 0;
    for (var i = 0; i < results.length; i++) {
        if (results[i]) passed++;
    }
    
    var summary = 'نتائج الاختبارات:\n';
    summary += 'نجح: ' + passed + ' من ' + results.length + '\n';
    
    if (passed === results.length) {
        summary += '🎉 جميع الاختبارات نجحت!';
        alert(summary);
    } else {
        summary += '⚠️ بعض الاختبارات فشلت!';
        alert(summary);
    }
}

// تشغيل الاختبارات تلقائياً
try {
    // تحميل إعدادات التسعير إذا لم تكن موجودة
    if (typeof PricingConfig === 'undefined') {
        alert('⚠️ PricingConfig غير موجود، سيتم إنشاء إعدادات افتراضية...');
        
        var PricingConfig = {
            designTypes: {
                logo: { name: 'لوجو', basePrice: 50 },
                brochure: { name: 'بروشور', basePrice: 40 },
                website: { name: 'موقع إلكتروني', basePrice: 60 }
            },
            complexityMultipliers: {
                simple: { name: 'بسيط', multiplier: 1.0 },
                medium: { name: 'متوسط', multiplier: 1.5 },
                complex: { name: 'معقد', multiplier: 2.0 },
                very_complex: { name: 'معقد جداً', multiplier: 2.5 }
            },
            usageMultipliers: {
                personal: { name: 'شخصي', multiplier: 1.0 },
                small_business: { name: 'مشروع صغير', multiplier: 1.4 },
                commercial: { name: 'تجاري', multiplier: 1.8 },
                enterprise: { name: 'مؤسسي', multiplier: 2.5 }
            },
            deliveryMultipliers: {
                normal: { name: 'عادي', multiplier: 1.0 },
                fast: { name: 'سريع', multiplier: 1.3 },
                urgent: { name: 'مستعجل', multiplier: 1.5 },
                express: { name: 'فوري', multiplier: 2.0 }
            },
            additionalCosts: {
                revisionCost: 15
            },
            settings: {
                minProjectValue: 50,
                maxProjectValue: 10000
            }
        };
    }
    
    // تشغيل الاختبارات
    runAllTests();
    
} catch (error) {
    alert('❌ خطأ في تشغيل الاختبارات: ' + error.message);
}
