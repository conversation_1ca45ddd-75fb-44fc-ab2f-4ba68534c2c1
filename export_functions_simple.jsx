/*
وظائف التصدير المبسطة - Simple Export Functions
نسخة مبسطة من وظائف التصدير للاستخدام المباشر
*/

// إضافة دعم Object.keys للإصدارات القديمة من ExtendScript
if (!Object.keys) {
    Object.keys = function(obj) {
        var keys = [];
        for (var key in obj) {
            if (obj.hasOwnProperty(key)) {
                keys.push(key);
            }
        }
        return keys;
    };
}

// دالة حفظ التقدير مع دعم اللغات
function saveEstimateWithLanguage(ui, price, language) {
    try {
        language = language || 'arabic';
        var dialogTitle = language === 'english' ? 
            'Save Price Estimate' : 
            'حفظ تقدير السعر';
        
        var file = File.saveDialog(dialogTitle, '*.txt');
        if (!file) return false;
        
        file.open('w');
        file.encoding = 'UTF-8';
        
        // رأس الملف
        file.writeln('═══════════════════════════════════════════════════════════');
        if (language === 'english') {
            file.writeln('                    Design Price Estimate                    ');
        } else if (language === 'bilingual') {
            file.writeln('            تقدير سعر التصميم / Design Price Estimate            ');
        } else {
            file.writeln('                    تقدير سعر التصميم                    ');
        }
        file.writeln('═══════════════════════════════════════════════════════════');
        file.writeln('');
        
        // معلومات التاريخ والوقت
        var now = new Date();
        var dateLabel = language === 'english' ? 'Date' : 'التاريخ';
        var timeLabel = language === 'english' ? 'Time' : 'الوقت';
        
        if (language === 'bilingual') {
            file.writeln('📅 التاريخ/Date: ' + now.toLocaleDateString());
            file.writeln('🕐 الوقت/Time: ' + now.toLocaleTimeString());
        } else {
            file.writeln('📅 ' + dateLabel + ': ' + now.toLocaleDateString());
            file.writeln('🕐 ' + timeLabel + ': ' + now.toLocaleTimeString());
        }
        file.writeln('');
        
        // تفاصيل المشروع
        var projectInfoLabel = language === 'english' ? 'Project Information' : 'معلومات المشروع';
        if (language === 'bilingual') projectInfoLabel = 'معلومات المشروع / Project Information';
        
        file.writeln('📋 ' + projectInfoLabel + ':');
        file.writeln('───────────────────────────────────────────────────────────');
        
        // نوع التصميم
        var designTypeIndex = ui.designTypeDropdown.selection.index;
        var designTypeKeys = Object.keys(PricingConfig.designTypes);
        var designType = PricingConfig.designTypes[designTypeKeys[designTypeIndex]];
        
        var designTypeLabel = language === 'english' ? 'Design Type' : 'نوع التصميم';
        var hoursLabel = language === 'english' ? 'Hours' : 'عدد الساعات';
        var complexityLabel = language === 'english' ? 'Complexity' : 'درجة التعقيد';
        var revisionsLabel = language === 'english' ? 'Revisions' : 'عدد التعديلات';
        
        if (language === 'bilingual') {
            designTypeLabel = 'نوع التصميم/Design Type';
            hoursLabel = 'عدد الساعات/Hours';
            complexityLabel = 'درجة التعقيد/Complexity';
            revisionsLabel = 'عدد التعديلات/Revisions';
        }
        
        file.writeln('🎨 ' + designTypeLabel + ': ' + designType.name);
        file.writeln('⏰ ' + hoursLabel + ': ' + ui.timeInput.text + (language === 'english' ? ' hours' : ' ساعة'));
        file.writeln('🔧 ' + complexityLabel + ': ' + ui.complexityValue.text);
        file.writeln('✏️ ' + revisionsLabel + ': ' + ui.revisionsInput.text);
        
        // نوع الاستخدام
        var usageType = 'شخصي';
        if (ui.usageSmallBusiness && ui.usageSmallBusiness.value) usageType = 'مشروع صغير';
        else if (ui.usageCommercial && ui.usageCommercial.value) usageType = 'تجاري';
        else if (ui.usageEnterprise && ui.usageEnterprise.value) usageType = 'مؤسسي';
        
        if (language === 'english') {
            usageType = 'Personal';
            if (ui.usageSmallBusiness && ui.usageSmallBusiness.value) usageType = 'Small Business';
            else if (ui.usageCommercial && ui.usageCommercial.value) usageType = 'Commercial';
            else if (ui.usageEnterprise && ui.usageEnterprise.value) usageType = 'Enterprise';
        } else if (language === 'bilingual') {
            var usageTypeEn = 'Personal';
            if (ui.usageSmallBusiness && ui.usageSmallBusiness.value) usageTypeEn = 'Small Business';
            else if (ui.usageCommercial && ui.usageCommercial.value) usageTypeEn = 'Commercial';
            else if (ui.usageEnterprise && ui.usageEnterprise.value) usageTypeEn = 'Enterprise';
            usageType = usageType + '/' + usageTypeEn;
        }
        
        var usageLabel = language === 'english' ? 'Usage Type' : 'نوع الاستخدام';
        if (language === 'bilingual') usageLabel = 'نوع الاستخدام/Usage Type';
        file.writeln('👥 ' + usageLabel + ': ' + usageType);
        
        // سرعة التسليم
        var deliveryType = 'عادي';
        if (ui.deliveryFast && ui.deliveryFast.value) deliveryType = 'سريع';
        else if (ui.deliveryUrgent && ui.deliveryUrgent.value) deliveryType = 'مستعجل';
        else if (ui.deliveryExpress && ui.deliveryExpress.value) deliveryType = 'فوري';
        
        if (language === 'english') {
            deliveryType = 'Normal';
            if (ui.deliveryFast && ui.deliveryFast.value) deliveryType = 'Fast';
            else if (ui.deliveryUrgent && ui.deliveryUrgent.value) deliveryType = 'Urgent';
            else if (ui.deliveryExpress && ui.deliveryExpress.value) deliveryType = 'Express';
        } else if (language === 'bilingual') {
            var deliveryTypeEn = 'Normal';
            if (ui.deliveryFast && ui.deliveryFast.value) deliveryTypeEn = 'Fast';
            else if (ui.deliveryUrgent && ui.deliveryUrgent.value) deliveryTypeEn = 'Urgent';
            else if (ui.deliveryExpress && ui.deliveryExpress.value) deliveryTypeEn = 'Express';
            deliveryType = deliveryType + '/' + deliveryTypeEn;
        }
        
        var deliveryLabel = language === 'english' ? 'Delivery Speed' : 'سرعة التسليم';
        if (language === 'bilingual') deliveryLabel = 'سرعة التسليم/Delivery Speed';
        file.writeln('🚀 ' + deliveryLabel + ': ' + deliveryType);
        file.writeln('');
        
        // تفاصيل الحساب
        var priceDetailsLabel = language === 'english' ? 'Price Calculation Details' : 'تفاصيل حساب السعر';
        if (language === 'bilingual') priceDetailsLabel = 'تفاصيل حساب السعر / Price Calculation Details';
        
        file.writeln('💰 ' + priceDetailsLabel + ':');
        file.writeln('───────────────────────────────────────────────────────────');
        
        // عرض تفاصيل الحساب من الواجهة
        if (ui.detailsDisplay && ui.detailsDisplay.text) {
            var details = ui.detailsDisplay.text;
            if (language === 'english') {
                // ترجمة بسيطة للتفاصيل
                details = details
                    .replace(/تفاصيل حساب السعر/g, 'Price Calculation Details')
                    .replace(/نوع التصميم/g, 'Design Type')
                    .replace(/عدد الساعات/g, 'Hours')
                    .replace(/السعر الأساسي/g, 'Base Price')
                    .replace(/معامل التعقيد/g, 'Complexity Factor')
                    .replace(/معامل الاستخدام/g, 'Usage Factor')
                    .replace(/معامل التسليم/g, 'Delivery Factor')
                    .replace(/السعر بعد المعاملات/g, 'Price after factors')
                    .replace(/تعديلات إضافية/g, 'Additional Revisions')
                    .replace(/التعديلات/g, 'Revisions')
                    .replace(/مشمولة مجاناً/g, 'included for free')
                    .replace(/السعر النهائي/g, 'Final Price')
                    .replace(/ساعة/g, 'hour');
            }
            file.writeln(details.replace(/\n/g, '\r\n'));
        }
        file.writeln('');
        
        // السعر النهائي
        var finalPriceLabel = language === 'english' ? 'Final Price' : 'السعر النهائي';
        if (language === 'bilingual') finalPriceLabel = 'السعر النهائي / Final Price';
        
        file.writeln('═══════════════════════════════════════════════════════════');
        file.writeln('🎯 ' + finalPriceLabel + ': ' + ui.currencySymbol.text + Math.round(price));
        file.writeln('═══════════════════════════════════════════════════════════');
        file.writeln('');
        
        // ملاحظات
        var notesLabel = language === 'english' ? 'Important Notes' : 'ملاحظات مهمة';
        if (language === 'bilingual') notesLabel = 'ملاحظات مهمة / Important Notes';
        
        file.writeln('📝 ' + notesLabel + ':');
        
        if (language === 'english') {
            file.writeln('• This estimate is valid for 30 days from the issue date');
            file.writeln('• Price does not include local taxes if applicable');
            file.writeln('• Additional changes require prior approval');
            file.writeln('• Delivery time starts from project approval date');
            file.writeln('• Ownership rights transfer to client upon full payment');
        } else if (language === 'bilingual') {
            file.writeln('• هذا التقدير صالح لمدة 30 يوماً / This estimate is valid for 30 days');
            file.writeln('• السعر لا يشمل الضرائب المحلية / Price does not include local taxes');
            file.writeln('• التعديلات الإضافية تتطلب موافقة مسبقة / Additional changes require approval');
            file.writeln('• مدة التسليم تبدأ من تاريخ الموافقة / Delivery time starts from approval');
            file.writeln('• حقوق الملكية تنتقل للعميل عند السداد الكامل / Rights transfer upon payment');
        } else {
            file.writeln('• هذا التقدير صالح لمدة 30 يوماً من تاريخ الإصدار');
            file.writeln('• السعر لا يشمل الضرائب المحلية إن وجدت');
            file.writeln('• التعديلات الإضافية تتطلب موافقة مسبقة');
            file.writeln('• مدة التسليم تبدأ من تاريخ الموافقة على المشروع');
            file.writeln('• حقوق الملكية تنتقل للعميل عند السداد الكامل');
        }
        file.writeln('');
        
        // تذييل
        file.writeln('───────────────────────────────────────────────────────────');
        if (language === 'english') {
            file.writeln('Generated by Design Price Calculator Pro v2.0');
            file.writeln('Professional Pricing Tool for Designers');
        } else if (language === 'bilingual') {
            file.writeln('تم إنشاؤه بواسطة حاسبة أسعار التصميم الاحترافية v2.0');
            file.writeln('Generated by Design Price Calculator Pro v2.0');
        } else {
            file.writeln('تم إنشاؤه بواسطة حاسبة أسعار التصميم الاحترافية v2.0');
            file.writeln('Design Price Calculator - Professional Pricing Tool');
        }
        file.writeln('───────────────────────────────────────────────────────────');
        
        file.close();
        
        alert('✅ تم حفظ التقدير بنجاح!\n\nFile saved at:\n' + file.fsName);
        return true;
        
    } catch (error) {
        alert('❌ خطأ في حفظ الملف: ' + error.message);
        return false;
    }
}

// دالة حفظ CSV متعدد اللغات
function saveAsCSVWithLanguage(ui, price, language) {
    try {
        language = language || 'arabic';
        var dialogTitle = language === 'english' ? 
            'Save Price Estimate - CSV' : 
            'حفظ تقدير السعر - CSV';
        
        var file = File.saveDialog(dialogTitle, '*.csv');
        if (!file) return false;
        
        file.open('w');
        file.encoding = 'UTF-8';
        
        // رأس CSV حسب اللغة
        var headers = [];
        if (language === 'english') {
            headers = ['Date', 'Time', 'Design Type', 'Hours', 'Complexity', 'Revisions', 'Usage Type', 'Delivery Speed', 'Final Price'];
        } else if (language === 'bilingual') {
            headers = ['التاريخ/Date', 'الوقت/Time', 'نوع التصميم/Design Type', 'الساعات/Hours', 'التعقيد/Complexity', 'التعديلات/Revisions', 'نوع الاستخدام/Usage Type', 'سرعة التسليم/Delivery Speed', 'السعر النهائي/Final Price'];
        } else {
            headers = ['التاريخ', 'الوقت', 'نوع التصميم', 'الساعات', 'التعقيد', 'التعديلات', 'نوع الاستخدام', 'سرعة التسليم', 'السعر النهائي'];
        }
        
        file.writeln(headers.join(','));
        
        // بيانات التقدير
        var now = new Date();
        var designTypeIndex = ui.designTypeDropdown.selection.index;
        var designTypeKeys = Object.keys(PricingConfig.designTypes);
        var designType = PricingConfig.designTypes[designTypeKeys[designTypeIndex]];
        
        var usageType = 'شخصي';
        if (ui.usageSmallBusiness && ui.usageSmallBusiness.value) usageType = 'مشروع صغير';
        else if (ui.usageCommercial && ui.usageCommercial.value) usageType = 'تجاري';
        else if (ui.usageEnterprise && ui.usageEnterprise.value) usageType = 'مؤسسي';
        
        var deliveryType = 'عادي';
        if (ui.deliveryFast && ui.deliveryFast.value) deliveryType = 'سريع';
        else if (ui.deliveryUrgent && ui.deliveryUrgent.value) deliveryType = 'مستعجل';
        else if (ui.deliveryExpress && ui.deliveryExpress.value) deliveryType = 'فوري';
        
        if (language === 'english') {
            usageType = 'Personal';
            if (ui.usageSmallBusiness && ui.usageSmallBusiness.value) usageType = 'Small Business';
            else if (ui.usageCommercial && ui.usageCommercial.value) usageType = 'Commercial';
            else if (ui.usageEnterprise && ui.usageEnterprise.value) usageType = 'Enterprise';
            
            deliveryType = 'Normal';
            if (ui.deliveryFast && ui.deliveryFast.value) deliveryType = 'Fast';
            else if (ui.deliveryUrgent && ui.deliveryUrgent.value) deliveryType = 'Urgent';
            else if (ui.deliveryExpress && ui.deliveryExpress.value) deliveryType = 'Express';
        }
        
        var csvLine = [
            now.toLocaleDateString(),
            now.toLocaleTimeString(),
            designType.name,
            ui.timeInput.text,
            Math.round(ui.complexitySlider.value),
            ui.revisionsInput.text,
            usageType,
            deliveryType,
            Math.round(price)
        ].join(',');
        
        file.writeln(csvLine);
        file.close();
        
        alert('✅ تم حفظ ملف CSV بنجاح!\n\nFile saved at:\n' + file.fsName);
        return true;
        
    } catch (error) {
        alert('❌ خطأ في حفظ ملف CSV: ' + error.message);
        return false;
    }
}

// دالة PDF بسيطة
function exportToPDF(ui, price) {
    alert('📄 مولد PDF المتقدم غير متاح حالياً.\n\n' +
          'سيتم حفظ التقدير كملف نصي منسق بدلاً من ذلك.\n\n' +
          'الملف النصي يحتوي على جميع المعلومات المطلوبة ويمكن تحويله إلى PDF لاحقاً.');
    
    return saveEstimateWithLanguage(ui, price, 'arabic');
}
