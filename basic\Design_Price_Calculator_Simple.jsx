/*
═══════════════════════════════════════════════════════════════════════════════
                    حاسبة أسعار التصميم البسيطة v1.1
                    Simple Design Price Calculator v1.1
═══════════════════════════════════════════════════════════════════════════════

نسخة مبسطة ومحسنة لتجنب أخطاء ExtendScript

الميزات:
✅ 5 أنواع تصميم أساسية
✅ 3 مستويات تعقيد
✅ 3 عملات أساسية
✅ حفظ نصي بسيط
✅ واجهة سهلة

═══════════════════════════════════════════════════════════════════════════════
*/

// بيانات التصميم (مصفوفات بسيطة لتجنب مشاكل Object.keys)
var designTypes = [
    { key: 'logo', name: 'تصميم شعار / Logo Design', basePrice: 150, baseHours: 6 },
    { key: 'brochure', name: 'بروشور/فلاير / Brochure/Flyer', basePrice: 100, baseHours: 4 },
    { key: 'business_card', name: 'كارت شخصي / Business Card', basePrice: 50, baseHours: 2 },
    { key: 'social_media', name: 'سوشيال ميديا / Social Media', basePrice: 75, baseHours: 3 },
    { key: 'poster', name: 'بوستر إعلاني / Advertising Poster', basePrice: 125, baseHours: 5 }
];

var complexityLevels = [
    { key: 'simple', name: 'بسيط / Simple', multiplier: 1.0 },
    { key: 'medium', name: 'متوسط / Medium', multiplier: 1.5 },
    { key: 'complex', name: 'معقد / Complex', multiplier: 2.0 }
];

var currencies = [
    { key: 'USD', symbol: '$', name: 'دولار أمريكي / US Dollar', rate: 1.0 },
    { key: 'SAR', symbol: 'ر.س', name: 'ريال سعودي / Saudi Riyal', rate: 3.75 },
    { key: 'AED', symbol: 'د.إ', name: 'درهم إماراتي / UAE Dirham', rate: 3.67 }
];

// متغيرات عامة
var currentPrice = 0;
var currentCurrency = 0; // فهرس العملة في المصفوفة
var selectedDesign = 0;
var selectedComplexity = 0;

// دالة إنشاء الواجهة
function createSimpleUI() {
    var dialog = new Window('dialog', '💎 حاسبة أسعار التصميم البسيطة');
    dialog.orientation = 'column';
    dialog.alignChildren = 'fill';
    dialog.spacing = 15;
    dialog.margins = 20;
    dialog.preferredSize.width = 480;
    dialog.preferredSize.height = 550;
    
    // العنوان
    var titlePanel = dialog.add('panel');
    titlePanel.orientation = 'column';
    titlePanel.alignChildren = 'center';
    titlePanel.margins = 15;
    
    var titleText = titlePanel.add('statictext', undefined, '💎 حاسبة أسعار التصميم البسيطة');
    titleText.graphics.font = ScriptUI.newFont('Arial', 'BOLD', 14);
    
    var subtitleText = titlePanel.add('statictext', undefined, 'Simple Design Price Calculator v1.1');
    
    // نوع التصميم
    var designGroup = dialog.add('panel', undefined, '🎨 نوع التصميم / Design Type');
    designGroup.orientation = 'column';
    designGroup.alignChildren = 'fill';
    designGroup.margins = 15;
    
    var designDropdown = designGroup.add('dropdownlist');
    for (var i = 0; i < designTypes.length; i++) {
        designDropdown.add('item', designTypes[i].name);
    }
    designDropdown.selection = 0;
    
    // ربط حدث تغيير نوع التصميم
    designDropdown.onChange = function() {
        selectedDesign = this.selection.index;
        if (currentPrice > 0) {
            calculatePrice();
        }
    };
    
    // مستوى التعقيد
    var complexityGroup = dialog.add('panel', undefined, '🔧 مستوى التعقيد / Complexity Level');
    complexityGroup.orientation = 'column';
    complexityGroup.alignChildren = 'fill';
    complexityGroup.margins = 15;
    
    var complexityRadios = [];
    for (var i = 0; i < complexityLevels.length; i++) {
        var radio = complexityGroup.add('radiobutton', undefined, complexityLevels[i].name);
        complexityRadios.push(radio);
        if (i === 0) radio.value = true; // تحديد الأول كافتراضي
        
        // ربط حدث تغيير التعقيد
        (function(index) {
            radio.onClick = function() {
                selectedComplexity = index;
                if (currentPrice > 0) {
                    calculatePrice();
                }
            };
        })(i);
    }
    
    // العملة
    var currencyGroup = dialog.add('panel', undefined, '💱 العملة / Currency');
    currencyGroup.orientation = 'column';
    currencyGroup.alignChildren = 'fill';
    currencyGroup.margins = 15;
    
    var currencyRadios = [];
    for (var i = 0; i < currencies.length; i++) {
        var radio = currencyGroup.add('radiobutton', undefined, currencies[i].symbol + ' ' + currencies[i].name);
        currencyRadios.push(radio);
        if (i === 0) radio.value = true; // USD كافتراضي
        
        // ربط حدث تغيير العملة
        (function(index) {
            radio.onClick = function() {
                currentCurrency = index;
                if (currentPrice > 0) {
                    updatePriceDisplay();
                }
            };
        })(i);
    }
    
    // النتائج
    var resultsGroup = dialog.add('panel', undefined, '💰 النتائج / Results');
    resultsGroup.orientation = 'column';
    resultsGroup.alignChildren = 'fill';
    resultsGroup.margins = 15;
    
    var priceDisplayGroup = resultsGroup.add('group');
    priceDisplayGroup.orientation = 'row';
    priceDisplayGroup.alignChildren = 'center';
    
    var currencySymbol = priceDisplayGroup.add('statictext', undefined, '$');
    currencySymbol.graphics.font = ScriptUI.newFont('Arial', 'BOLD', 20);
    
    var priceDisplay = priceDisplayGroup.add('statictext', undefined, '0');
    priceDisplay.graphics.font = ScriptUI.newFont('Arial', 'BOLD', 20);
    priceDisplay.preferredSize.width = 120;
    
    // تفاصيل الحساب
    var detailsDisplay = resultsGroup.add('edittext', undefined, '', {multiline: true, readonly: true});
    detailsDisplay.preferredSize.height = 100;
    
    // الأزرار
    var buttonsGroup = dialog.add('group');
    buttonsGroup.orientation = 'row';
    buttonsGroup.alignChildren = 'center';
    buttonsGroup.spacing = 10;
    
    var calculateButton = buttonsGroup.add('button', undefined, '🧮 احسب السعر');
    calculateButton.preferredSize.width = 120;
    
    var exportButton = buttonsGroup.add('button', undefined, '📁 حفظ');
    exportButton.preferredSize.width = 80;
    
    var closeButton = buttonsGroup.add('button', undefined, '❌ إغلاق');
    closeButton.preferredSize.width = 80;
    
    // دالة تحديث عرض السعر
    function updatePriceDisplay() {
        var currency = currencies[currentCurrency];
        var convertedPrice = currentPrice * currency.rate;
        currencySymbol.text = currency.symbol;
        priceDisplay.text = Math.round(convertedPrice).toString();
    }
    
    // دالة حساب السعر
    function calculatePrice() {
        try {
            var designType = designTypes[selectedDesign];
            var complexity = complexityLevels[selectedComplexity];
            
            // حساب السعر
            var basePrice = designType.basePrice;
            var finalPrice = basePrice * complexity.multiplier;
            
            currentPrice = finalPrice;
            updatePriceDisplay();
            
            // تحديث التفاصيل
            var details = '';
            details += '📋 تفاصيل الحساب:\n';
            details += '─────────────────────\n';
            details += '🎨 التصميم: ' + designType.name + '\n';
            details += '💰 السعر الأساسي: $' + basePrice + '\n';
            details += '🔧 التعقيد: ' + complexity.name + '\n';
            details += '📊 المعامل: x' + complexity.multiplier + '\n';
            details += '─────────────────────\n';
            details += '🎯 السعر النهائي: $' + Math.round(finalPrice) + '\n';
            details += '💱 بالعملة المحددة: ' + currencies[currentCurrency].symbol + Math.round(finalPrice * currencies[currentCurrency].rate);
            
            detailsDisplay.text = details;
            
            return finalPrice;
            
        } catch (error) {
            alert('❌ خطأ في الحساب: ' + error.message);
            return 0;
        }
    }
    
    // دالة حفظ التقدير
    function saveEstimate() {
        if (currentPrice <= 0) {
            alert('⚠️ يرجى حساب السعر أولاً');
            return;
        }
        
        try {
            var file = File.saveDialog('حفظ تقدير السعر', '*.txt');
            if (!file) return;
            
            file.open('w');
            file.encoding = 'UTF-8';
            
            var designType = designTypes[selectedDesign];
            var complexity = complexityLevels[selectedComplexity];
            var currency = currencies[currentCurrency];
            var convertedPrice = currentPrice * currency.rate;
            
            file.writeln('═══════════════════════════════════════════════════════════');
            file.writeln('            تقدير سعر التصميم / Design Price Estimate            ');
            file.writeln('═══════════════════════════════════════════════════════════');
            file.writeln('');
            
            var now = new Date();
            file.writeln('📅 التاريخ / Date: ' + now.toLocaleDateString());
            file.writeln('🕐 الوقت / Time: ' + now.toLocaleTimeString());
            file.writeln('');
            
            file.writeln('📋 تفاصيل المشروع / Project Details:');
            file.writeln('───────────────────────────────────────────────────────────');
            file.writeln('🎨 نوع التصميم / Design Type: ' + designType.name);
            file.writeln('🔧 مستوى التعقيد / Complexity: ' + complexity.name);
            file.writeln('💰 السعر الأساسي / Base Price: $' + designType.basePrice);
            file.writeln('📊 معامل التعقيد / Multiplier: x' + complexity.multiplier);
            file.writeln('');
            
            file.writeln('═══════════════════════════════════════════════════════════');
            file.writeln('🎯 السعر النهائي / Final Price: $' + Math.round(currentPrice));
            file.writeln('💱 بالعملة المحددة / In Selected Currency: ' + currency.symbol + Math.round(convertedPrice));
            file.writeln('═══════════════════════════════════════════════════════════');
            file.writeln('');
            
            file.writeln('📝 ملاحظات / Notes:');
            file.writeln('• هذا التقدير صالح لمدة 30 يوماً / Valid for 30 days');
            file.writeln('• السعر لا يشمل الضرائب / Price excludes taxes');
            file.writeln('• التعديلات الإضافية قد تتطلب رسوم إضافية');
            file.writeln('  Additional revisions may require extra fees');
            file.writeln('');
            
            file.writeln('───────────────────────────────────────────────────────────');
            file.writeln('تم إنشاؤه بواسطة حاسبة أسعار التصميم البسيطة v1.1');
            file.writeln('Generated by Simple Design Price Calculator v1.1');
            file.writeln('───────────────────────────────────────────────────────────');
            
            file.close();
            
            alert('✅ تم حفظ التقدير بنجاح!\n\nFile: ' + file.fsName);
            
        } catch (error) {
            alert('❌ خطأ في حفظ الملف: ' + error.message);
        }
    }
    
    // ربط الأحداث
    calculateButton.onClick = function() {
        calculatePrice();
    };
    
    exportButton.onClick = function() {
        saveEstimate();
    };
    
    closeButton.onClick = function() {
        dialog.close();
    };
    
    return dialog;
}

// دالة رئيسية
function runSimpleCalculator() {
    try {
        alert('🎉 مرحباً بك في حاسبة أسعار التصميم البسيطة v1.1\n\n' +
              '✨ الميزات:\n' +
              '🎨 5 أنواع تصميم أساسية\n' +
              '🔧 3 مستويات تعقيد\n' +
              '💱 3 عملات أساسية\n' +
              '📁 حفظ نصي بسيط\n\n' +
              'تم إصلاح مشاكل التوافق مع ExtendScript\n\n' +
              'استمتع بالحساب السريع! 🚀');
        
        var ui = createSimpleUI();
        ui.show();
        
    } catch (error) {
        alert('❌ خطأ في تشغيل الحاسبة: ' + error.message);
    }
}

// تشغيل الحاسبة
runSimpleCalculator();
