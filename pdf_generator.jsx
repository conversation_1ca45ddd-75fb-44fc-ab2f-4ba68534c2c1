/*
مولد PDF للعروض الاحترافية - Professional PDF Quote Generator
يقوم بإنشاء عروض أسعار احترافية بصيغة PDF
*/

// إضافة دعم Object.keys للإصدارات القديمة من ExtendScript
if (!Object.keys) {
    Object.keys = function(obj) {
        var keys = [];
        for (var key in obj) {
            if (obj.hasOwnProperty(key)) {
                keys.push(key);
            }
        }
        return keys;
    };
}

// إعدادات PDF
var PDFConfig = {
    // إعدادات الصفحة
    pageSize: {
        width: 595, // A4 width in points
        height: 842  // A4 height in points
    },
    
    // الهوامش
    margins: {
        top: 50,
        bottom: 50,
        left: 50,
        right: 50
    },
    
    // الألوان
    colors: {
        primary: [0.2, 0.4, 0.8],      // أزرق
        secondary: [0.5, 0.5, 0.5],    // رمادي
        accent: [0.9, 0.6, 0.1],       // برتقالي
        text: [0.1, 0.1, 0.1],         // أسود
        background: [0.98, 0.98, 0.98] // أبيض مائل للرمادي
    },
    
    // الخطوط
    fonts: {
        title: { name: 'Arial-Bold', size: 24 },
        heading: { name: 'Arial-Bold', size: 16 },
        subheading: { name: 'Arial-Bold', size: 12 },
        body: { name: 'Arial', size: 10 },
        small: { name: 'Arial', size: 8 }
    },
    
    // معلومات الشركة الافتراضية
    company: {
        name: 'استوديو التصميم الاحترافي',
        nameEn: 'Professional Design Studio',
        address: 'الرياض، المملكة العربية السعودية',
        addressEn: 'Riyadh, Saudi Arabia',
        phone: '+966 50 123 4567',
        email: '<EMAIL>',
        website: 'www.designstudio.com',
        logo: '' // مسار الشعار
    }
};

// دالة إنشاء وثيقة PDF جديدة (محاكاة)
function createPDFDocument() {
    try {
        // في Photoshop، سننشئ وثيقة جديدة لمحاكاة PDF
        var doc = app.documents.add(
            PDFConfig.pageSize.width,
            PDFConfig.pageSize.height,
            72, // DPI
            'Quote_' + new Date().getTime(),
            NewDocumentMode.RGB
        );
        
        return doc;
        
    } catch (error) {
        alert('❌ خطأ في إنشاء وثيقة PDF: ' + error.message);
        return null;
    }
}

// دالة إضافة نص للوثيقة
function addTextToPDF(doc, text, x, y, font, color) {
    try {
        var textLayer = doc.artLayers.add();
        textLayer.kind = LayerKind.TEXT;
        textLayer.name = 'Text_' + new Date().getTime();
        
        var textItem = textLayer.textItem;
        textItem.contents = text;
        textItem.position = [x, y];
        textItem.size = font.size;
        
        // تطبيق اللون
        var textColor = new SolidColor();
        textColor.rgb.red = color[0] * 255;
        textColor.rgb.green = color[1] * 255;
        textColor.rgb.blue = color[2] * 255;
        textItem.color = textColor;
        
        return textLayer;
        
    } catch (error) {
        // في حالة فشل إضافة النص، نتجاهل الخطأ
        return null;
    }
}

// دالة إنشاء عرض سعر PDF احترافي
function generateProfessionalQuote(ui, price, language, clientInfo) {
    try {
        language = language || 'arabic';
        clientInfo = clientInfo || {};
        
        // إنشاء الوثيقة
        var doc = createPDFDocument();
        if (!doc) return false;
        
        var currentY = PDFConfig.margins.top;
        var lineHeight = 20;
        
        // رأس الوثيقة
        addTextToPDF(doc, 
            language === 'english' ? PDFConfig.company.nameEn : PDFConfig.company.name,
            PDFConfig.margins.left, currentY, 
            PDFConfig.fonts.title, PDFConfig.colors.primary);
        
        currentY += 40;
        
        // معلومات الشركة
        var companyInfo = language === 'english' ? 
            PDFConfig.company.addressEn + '\n' + PDFConfig.company.phone + '\n' + PDFConfig.company.email :
            PDFConfig.company.address + '\n' + PDFConfig.company.phone + '\n' + PDFConfig.company.email;
        
        addTextToPDF(doc, companyInfo, PDFConfig.margins.left, currentY, 
            PDFConfig.fonts.body, PDFConfig.colors.text);
        
        currentY += 80;
        
        // عنوان العرض
        var quoteTitle = language === 'english' ? 'DESIGN PRICE QUOTE' : 'عرض سعر التصميم';
        addTextToPDF(doc, quoteTitle, PDFConfig.margins.left, currentY, 
            PDFConfig.fonts.heading, PDFConfig.colors.primary);
        
        currentY += 40;
        
        // معلومات العميل
        if (clientInfo.name) {
            var clientLabel = language === 'english' ? 'Client: ' : 'العميل: ';
            addTextToPDF(doc, clientLabel + clientInfo.name, 
                PDFConfig.margins.left, currentY, 
                PDFConfig.fonts.body, PDFConfig.colors.text);
            currentY += lineHeight;
        }
        
        // التاريخ
        var dateLabel = language === 'english' ? 'Date: ' : 'التاريخ: ';
        addTextToPDF(doc, dateLabel + new Date().toLocaleDateString(), 
            PDFConfig.margins.left, currentY, 
            PDFConfig.fonts.body, PDFConfig.colors.text);
        
        currentY += 40;
        
        // تفاصيل المشروع
        var projectLabel = language === 'english' ? 'PROJECT DETAILS' : 'تفاصيل المشروع';
        addTextToPDF(doc, projectLabel, PDFConfig.margins.left, currentY, 
            PDFConfig.fonts.heading, PDFConfig.colors.secondary);
        
        currentY += 30;
        
        // نوع التصميم
        var designTypeIndex = ui.designTypeDropdown.selection.index;
        var designTypeKeys = Object.keys(PricingConfig.designTypes);
        var designType = PricingConfig.designTypes[designTypeKeys[designTypeIndex]];
        
        var designTypeLabel = language === 'english' ? 'Design Type: ' : 'نوع التصميم: ';
        var designTypeName = language === 'english' ? 
            (designType.nameEn || designType.name) : designType.name;
        
        addTextToPDF(doc, designTypeLabel + designTypeName, 
            PDFConfig.margins.left, currentY, 
            PDFConfig.fonts.body, PDFConfig.colors.text);
        
        currentY += lineHeight;
        
        // عدد الساعات
        var hoursLabel = language === 'english' ? 'Estimated Hours: ' : 'عدد الساعات المقدرة: ';
        var hoursUnit = language === 'english' ? ' hours' : ' ساعة';
        addTextToPDF(doc, hoursLabel + ui.timeInput.text + hoursUnit, 
            PDFConfig.margins.left, currentY, 
            PDFConfig.fonts.body, PDFConfig.colors.text);
        
        currentY += lineHeight;
        
        // درجة التعقيد
        var complexityLabel = language === 'english' ? 'Complexity Level: ' : 'درجة التعقيد: ';
        addTextToPDF(doc, complexityLabel + ui.complexityValue.text, 
            PDFConfig.margins.left, currentY, 
            PDFConfig.fonts.body, PDFConfig.colors.text);
        
        currentY += 40;
        
        // السعر النهائي
        var priceLabel = language === 'english' ? 'TOTAL PRICE' : 'السعر الإجمالي';
        addTextToPDF(doc, priceLabel, PDFConfig.margins.left, currentY, 
            PDFConfig.fonts.heading, PDFConfig.colors.primary);
        
        currentY += 30;
        
        var finalPriceText = ui.currencySymbol.text + Math.round(price);
        addTextToPDF(doc, finalPriceText, PDFConfig.margins.left, currentY, 
            PDFConfig.fonts.title, PDFConfig.colors.accent);
        
        currentY += 60;
        
        // الشروط والأحكام
        var termsLabel = language === 'english' ? 'TERMS & CONDITIONS' : 'الشروط والأحكام';
        addTextToPDF(doc, termsLabel, PDFConfig.margins.left, currentY, 
            PDFConfig.fonts.heading, PDFConfig.colors.secondary);
        
        currentY += 30;
        
        var terms = '';
        if (language === 'english') {
            terms = '• This quote is valid for 30 days from the issue date\n';
            terms += '• 50% payment required upfront, 50% upon completion\n';
            terms += '• Price includes 2 free revisions\n';
            terms += '• Additional revisions: $15 each\n';
            terms += '• Final files delivered upon full payment';
        } else {
            terms = '• هذا العرض صالح لمدة 30 يوماً من تاريخ الإصدار\n';
            terms += '• مطلوب دفع 50% مقدماً، و50% عند الانتهاء\n';
            terms += '• السعر يشمل تعديلين مجانيين\n';
            terms += '• التعديلات الإضافية: 15$ لكل تعديل\n';
            terms += '• تسليم الملفات النهائية عند السداد الكامل';
        }
        
        addTextToPDF(doc, terms, PDFConfig.margins.left, currentY, 
            PDFConfig.fonts.body, PDFConfig.colors.text);
        
        // ملاحظات العميل إذا كانت موجودة
        if (clientInfo.notes && clientInfo.notes.length > 0) {
            currentY += 100;
            var notesLabel = language === 'english' ? 'ADDITIONAL NOTES' : 'ملاحظات إضافية';
            addTextToPDF(doc, notesLabel, PDFConfig.margins.left, currentY, 
                PDFConfig.fonts.heading, PDFConfig.colors.secondary);
            
            currentY += 30;
            addTextToPDF(doc, clientInfo.notes, PDFConfig.margins.left, currentY, 
                PDFConfig.fonts.body, PDFConfig.colors.text);
        }
        
        // تذييل الصفحة
        var footerY = PDFConfig.pageSize.height - PDFConfig.margins.bottom - 20;
        var footerText = language === 'english' ? 
            'Generated by Professional Design Price Calculator v2.0' :
            'تم إنشاؤه بواسطة حاسبة أسعار التصميم الاحترافية v2.0';
        
        addTextToPDF(doc, footerText, PDFConfig.margins.left, footerY, 
            PDFConfig.fonts.small, PDFConfig.colors.secondary);
        
        // حفظ الوثيقة
        var fileName = 'Quote_' + (clientInfo.name || 'Client') + '_' + new Date().getTime();
        
        alert('✅ تم إنشاء عرض السعر بنجاح!\n\n' +
              '📄 اسم الملف: ' + fileName + '\n' +
              '💡 ملاحظة: تم إنشاء الملف كوثيقة Photoshop.\n' +
              'يمكنك حفظها كـ PDF من قائمة File > Export > Export As');
        
        return true;
        
    } catch (error) {
        alert('❌ خطأ في إنشاء عرض السعر: ' + error.message);
        return false;
    }
}

// دالة عرض نافذة إعدادات PDF
function showPDFSettings(ui, price) {
    var dialog = new Window('dialog', 'إعدادات عرض السعر - Quote Settings');
    dialog.orientation = 'column';
    dialog.alignChildren = 'fill';
    dialog.spacing = 15;
    dialog.margins = 20;
    dialog.preferredSize.width = 500;
    
    var title = dialog.add('statictext', undefined, '📄 إنشاء عرض سعر احترافي');
    title.graphics.font = ScriptUI.newFont(title.graphics.font.name, ScriptUI.FontStyle.BOLD, 16);
    
    // معلومات العميل
    var clientGroup = dialog.add('panel', undefined, 'معلومات العميل');
    clientGroup.orientation = 'column';
    clientGroup.alignChildren = 'fill';
    clientGroup.margins = 15;
    
    var nameRow = clientGroup.add('group');
    nameRow.add('statictext', undefined, 'اسم العميل:').preferredSize.width = 100;
    var clientNameInput = nameRow.add('edittext', undefined, AdvancedConfig.clientName || '');
    clientNameInput.preferredSize.width = 300;
    
    var emailRow = clientGroup.add('group');
    emailRow.add('statictext', undefined, 'البريد الإلكتروني:').preferredSize.width = 100;
    var clientEmailInput = emailRow.add('edittext', undefined, '');
    clientEmailInput.preferredSize.width = 300;
    
    // إعدادات اللغة
    var languageGroup = dialog.add('panel', undefined, 'إعدادات اللغة');
    languageGroup.orientation = 'column';
    languageGroup.alignChildren = 'fill';
    languageGroup.margins = 15;
    
    var arabicLang = languageGroup.add('radiobutton', undefined, '🇸🇦 العربية');
    var englishLang = languageGroup.add('radiobutton', undefined, '🇺🇸 الإنجليزية');
    var bilingualLang = languageGroup.add('radiobutton', undefined, '🌍 ثنائي اللغة');
    arabicLang.value = true;
    
    // معاينة السعر
    var priceGroup = dialog.add('panel', undefined, 'معاينة السعر');
    priceGroup.orientation = 'row';
    priceGroup.alignChildren = 'center';
    priceGroup.margins = 15;
    
    var priceLabel = priceGroup.add('statictext', undefined, 'السعر النهائي:');
    priceLabel.graphics.font = ScriptUI.newFont(priceLabel.graphics.font.name, ScriptUI.FontStyle.BOLD, 14);
    
    var priceDisplay = priceGroup.add('statictext', undefined, ui.currencySymbol.text + Math.round(price));
    priceDisplay.graphics.font = ScriptUI.newFont(priceDisplay.graphics.font.name, ScriptUI.FontStyle.BOLD, 18);
    
    // أزرار التحكم
    var buttonGroup = dialog.add('group');
    buttonGroup.orientation = 'row';
    buttonGroup.alignChildren = 'center';
    buttonGroup.spacing = 15;
    
    var generateButton = buttonGroup.add('button', undefined, '📄 إنشاء عرض السعر');
    var previewButton = buttonGroup.add('button', undefined, '👁️ معاينة');
    var cancelButton = buttonGroup.add('button', undefined, '❌ إلغاء');
    
    var result = { generated: false };
    
    generateButton.onClick = function() {
        var selectedLanguage = 'arabic';
        if (englishLang.value) selectedLanguage = 'english';
        else if (bilingualLang.value) selectedLanguage = 'bilingual';
        
        var clientInfo = {
            name: clientNameInput.text,
            email: clientEmailInput.text,
            notes: AdvancedConfig.clientNotes || ''
        };
        
        var success = generateProfessionalQuote(ui, price, selectedLanguage, clientInfo);
        if (success) {
            result.generated = true;
            dialog.close();
        }
    };
    
    previewButton.onClick = function() {
        alert('📋 معاينة عرض السعر:\n\n' +
              '👤 العميل: ' + (clientNameInput.text || 'غير محدد') + '\n' +
              '💰 السعر: ' + ui.currencySymbol.text + Math.round(price) + '\n' +
              '📅 التاريخ: ' + new Date().toLocaleDateString() + '\n' +
              '🌐 اللغة: ' + (arabicLang.value ? 'العربية' : englishLang.value ? 'الإنجليزية' : 'ثنائي اللغة'));
    };
    
    cancelButton.onClick = function() {
        dialog.close();
    };
    
    dialog.show();
    return result;
}

// دالة تصدير PDF مبسطة (للاستخدام مع الأزرار)
function exportToPDF(ui, price) {
    if (price <= 0) {
        alert('⚠️ يرجى حساب السعر أولاً قبل إنشاء عرض السعر');
        return false;
    }
    
    return showPDFSettings(ui, price);
}
