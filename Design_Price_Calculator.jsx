/*
Design Price Calculator Script for Adobe Photoshop
حاسبة أسعار التصميم لبرنامج أدوبي فوتوشوب

Author: Design Price Calculator
Version: 1.0
Description: سكريبت لحساب أسعار التصميم بناءً على معايير مختلفة

Features:
- حساب الأسعار بناءً على معايير متعددة
- واجهة مستخدم سهلة الاستخدام
- تصدير التقديرات بصيغ مختلفة
- نظام تسعير مرن وقابل للتخصيص
*/

// التحقق من توفر فوتوشوب
if (typeof app === 'undefined' || app.name !== 'Adobe Photoshop') {
    alert('هذا السكريبت يعمل فقط في برنامج Adobe Photoshop');
}

// إضافة دعم Object.keys للإصدارات القديمة من ExtendScript
if (!Object.keys) {
    Object.keys = function(obj) {
        var keys = [];
        for (var key in obj) {
            if (obj.hasOwnProperty(key)) {
                keys.push(key);
            }
        }
        return keys;
    };
}

// تحميل ملفات الإعدادات والوظائف الإضافية
try {
    // محاولة تحميل ملف الإعدادات
    var configFile = new File($.fileName.replace(/[^\/\\]*$/, '') + 'pricing_config.jsx');
    if (configFile.exists) {
        $.evalFile(configFile);
    }

    // محاولة تحميل وظائف التصدير
    var exportFile = new File($.fileName.replace(/[^\/\\]*$/, '') + 'export_functions.jsx');
    if (exportFile.exists) {
        $.evalFile(exportFile);
    }
} catch (e) {
    // في حالة عدم وجود الملفات الإضافية، استخدم الإعدادات المدمجة
}

// إعدادات التسعير الافتراضية
var PricingConfig = {
    // أسعار أساسية لكل نوع تصميم (بالدولار/ساعة)
    designTypes: {
        'logo': { name: 'لوجو', basePrice: 50, complexity: 1.5 },
        'social_media': { name: 'سوشيال ميديا', basePrice: 25, complexity: 1.0 },
        'brand_identity': { name: 'هوية بصرية', basePrice: 75, complexity: 2.0 },
        'poster': { name: 'بوستر', basePrice: 40, complexity: 1.3 },
        'ui_ux': { name: 'UI/UX', basePrice: 60, complexity: 1.8 },
        'business_card': { name: 'كارت شخصي', basePrice: 30, complexity: 1.0 },
        'brochure': { name: 'بروشور', basePrice: 45, complexity: 1.4 },
        'packaging': { name: 'تصميم عبوات', basePrice: 80, complexity: 2.2 }
    },
    
    // معاملات التعقيد
    complexityMultipliers: {
        'simple': { name: 'بسيط', multiplier: 1.0 },
        'medium': { name: 'متوسط', multiplier: 1.5 },
        'complex': { name: 'معقد', multiplier: 2.0 }
    },
    
    // معاملات نوع الاستخدام
    usageMultipliers: {
        'personal': { name: 'شخصي', multiplier: 1.0 },
        'commercial': { name: 'تجاري', multiplier: 1.8 }
    },
    
    // معاملات سرعة التسليم
    deliveryMultipliers: {
        'normal': { name: 'عادي', multiplier: 1.0 },
        'urgent': { name: 'مستعجل', multiplier: 1.5 }
    },
    
    // تكلفة إضافية لكل تعديل
    revisionCost: 15
};

// دالة إنشاء واجهة المستخدم
function createUI() {
    var dialog = new Window('dialog', 'حاسبة أسعار التصميم - Design Price Calculator');
    dialog.orientation = 'column';
    dialog.alignChildren = 'fill';
    dialog.spacing = 10;
    dialog.margins = 20;
    dialog.preferredSize.width = 500;

    // عنوان رئيسي مع أيقونة
    var headerGroup = dialog.add('panel');
    headerGroup.orientation = 'column';
    headerGroup.alignChildren = 'center';
    headerGroup.margins = 15;

    var titleGroup = headerGroup.add('group');
    titleGroup.orientation = 'row';
    titleGroup.alignChildren = 'center';

    var title = titleGroup.add('statictext', undefined, '💰 حاسبة أسعار التصميم');
    title.graphics.font = ScriptUI.newFont(title.graphics.font.name, ScriptUI.FontStyle.BOLD, 18);

    var subtitle = headerGroup.add('statictext', undefined, 'احسب سعر مشروعك بدقة واحترافية');
    subtitle.graphics.font = ScriptUI.newFont(subtitle.graphics.font.name, ScriptUI.FontStyle.ITALIC, 12);
    
    // مجموعة نوع التصميم
    var designTypeGroup = dialog.add('panel', undefined, '🎨 نوع التصميم');
    designTypeGroup.orientation = 'column';
    designTypeGroup.alignChildren = 'fill';
    designTypeGroup.margins = 15;

    var designTypeRow = designTypeGroup.add('group');
    designTypeRow.orientation = 'row';
    designTypeRow.alignChildren = 'left';

    var designTypeLabel = designTypeRow.add('statictext', undefined, 'اختر نوع التصميم:');
    designTypeLabel.preferredSize.width = 130;

    var designTypeDropdown = designTypeRow.add('dropdownlist');
    designTypeDropdown.preferredSize.width = 250;

    // إضافة خيارات نوع التصميم مع الأسعار
    for (var key in PricingConfig.designTypes) {
        var item = PricingConfig.designTypes[key];
        designTypeDropdown.add('item', item.name + ' ($' + item.basePrice + '/ساعة)');
    }
    designTypeDropdown.selection = 0;

    // عرض وصف نوع التصميم
    var designDescription = designTypeGroup.add('statictext', undefined, '');
    designDescription.preferredSize.width = 400;
    designDescription.preferredSize.height = 30;

    // تحديث الوصف عند تغيير النوع
    designTypeDropdown.onChange = function() {
        var keys = Object.keys(PricingConfig.designTypes);
        var selectedKey = keys[this.selection.index];
        var selectedType = PricingConfig.designTypes[selectedKey];
        designDescription.text = selectedType.description || '';
    };

    // تعيين الوصف الافتراضي
    if (designTypeDropdown.selection) {
        designTypeDropdown.onChange();
    }
    
    // مجموعة الوقت المتوقع
    var timeGroup = dialog.add('panel', undefined, '⏰ الوقت المتوقع');
    timeGroup.orientation = 'column';
    timeGroup.alignChildren = 'fill';
    timeGroup.margins = 15;

    var timeRow = timeGroup.add('group');
    timeRow.orientation = 'row';
    timeRow.alignChildren = 'left';

    var timeLabel = timeRow.add('statictext', undefined, 'عدد الساعات:');
    timeLabel.preferredSize.width = 130;

    var timeInput = timeRow.add('edittext', undefined, '5');
    timeInput.preferredSize.width = 80;
    timeInput.characters = 10;

    var timeHint = timeRow.add('statictext', undefined, 'ساعة');
    timeHint.preferredSize.width = 50;

    // شريط توضيحي للوقت
    var timeHintGroup = timeGroup.add('group');
    timeHintGroup.orientation = 'row';
    timeHintGroup.alignChildren = 'left';

    var timeHintText = timeHintGroup.add('statictext', undefined, '💡 نصيحة: لوجو بسيط (3-5 ساعات)، معقد (8-15 ساعة)');
    timeHintText.graphics.font = ScriptUI.newFont(timeHintText.graphics.font.name, ScriptUI.FontStyle.ITALIC, 10);
    
    // مجموعة درجة التعقيد
    var complexityGroup = dialog.add('panel', undefined, '🔧 درجة التعقيد');
    complexityGroup.orientation = 'column';
    complexityGroup.alignChildren = 'fill';
    complexityGroup.margins = 15;

    var complexitySliderGroup = complexityGroup.add('group');
    complexitySliderGroup.orientation = 'row';
    complexitySliderGroup.alignChildren = 'center';

    var complexityLabel = complexitySliderGroup.add('statictext', undefined, 'التعقيد:');
    complexityLabel.preferredSize.width = 70;

    var complexitySlider = complexitySliderGroup.add('slider', undefined, 1, 1, 4);
    complexitySlider.preferredSize.width = 250;

    var complexityValue = complexitySliderGroup.add('statictext', undefined, 'بسيط (x1.0)');
    complexityValue.preferredSize.width = 100;

    // مؤشرات التعقيد
    var complexityIndicators = complexityGroup.add('group');
    complexityIndicators.orientation = 'row';
    complexityIndicators.alignChildren = 'center';

    var indicator1 = complexityIndicators.add('statictext', undefined, 'بسيط');
    var indicator2 = complexityIndicators.add('statictext', undefined, 'متوسط');
    var indicator3 = complexityIndicators.add('statictext', undefined, 'معقد');
    var indicator4 = complexityIndicators.add('statictext', undefined, 'معقد جداً');

    indicator1.preferredSize.width = indicator2.preferredSize.width =
    indicator3.preferredSize.width = indicator4.preferredSize.width = 100;

    // تحديث نص التعقيد عند تحريك المنزلق
    complexitySlider.onChanging = function() {
        var value = Math.round(this.value);
        var complexityKeys = Object.keys(PricingConfig.complexityMultipliers);
        var complexity = PricingConfig.complexityMultipliers[complexityKeys[value - 1]];
        complexityValue.text = complexity.name + ' (x' + complexity.multiplier + ')';

        // تحديث ألوان المؤشرات (محاكاة)
        indicator1.text = value >= 1 ? '● بسيط' : '○ بسيط';
        indicator2.text = value >= 2 ? '● متوسط' : '○ متوسط';
        indicator3.text = value >= 3 ? '● معقد' : '○ معقد';
        indicator4.text = value >= 4 ? '● معقد جداً' : '○ معقد جداً';
    };

    // تعيين القيمة الافتراضية
    complexitySlider.onChanging();
    
    // مجموعة عدد التعديلات
    var revisionsGroup = dialog.add('panel', undefined, '✏️ التعديلات');
    revisionsGroup.orientation = 'column';
    revisionsGroup.alignChildren = 'fill';
    revisionsGroup.margins = 15;

    var revisionsRow = revisionsGroup.add('group');
    revisionsRow.orientation = 'row';
    revisionsRow.alignChildren = 'left';

    var revisionsLabel = revisionsRow.add('statictext', undefined, 'عدد التعديلات:');
    revisionsLabel.preferredSize.width = 130;

    var revisionsInput = revisionsRow.add('edittext', undefined, '2');
    revisionsInput.preferredSize.width = 80;
    revisionsInput.characters = 10;

    var revisionsCost = revisionsRow.add('statictext', undefined, '($' + PricingConfig.revisionCost + ' لكل تعديل)');
    revisionsCost.preferredSize.width = 150;

    var revisionsHint = revisionsGroup.add('statictext', undefined, '💡 التعديلات الأساسية مشمولة، التعديلات الإضافية برسوم');
    revisionsHint.graphics.font = ScriptUI.newFont(revisionsHint.graphics.font.name, ScriptUI.FontStyle.ITALIC, 10);
    
    // مجموعة نوع الاستخدام
    var usageGroup = dialog.add('panel', undefined, '👥 نوع الاستخدام');
    usageGroup.orientation = 'column';
    usageGroup.alignChildren = 'fill';
    usageGroup.margins = 15;

    var usageRow = usageGroup.add('group');
    usageRow.orientation = 'row';
    usageRow.alignChildren = 'left';

    var usagePersonal = usageRow.add('radiobutton', undefined, 'شخصي (x1.0)');
    var usageSmallBusiness = usageRow.add('radiobutton', undefined, 'مشروع صغير (x1.4)');
    var usageCommercial = usageRow.add('radiobutton', undefined, 'تجاري (x1.8)');
    var usageEnterprise = usageRow.add('radiobutton', undefined, 'مؤسسي (x2.5)');
    usagePersonal.value = true;

    // مجموعة سرعة التسليم
    var deliveryGroup = dialog.add('panel', undefined, '🚀 سرعة التسليم');
    deliveryGroup.orientation = 'column';
    deliveryGroup.alignChildren = 'fill';
    deliveryGroup.margins = 15;

    var deliveryRow = deliveryGroup.add('group');
    deliveryRow.orientation = 'row';
    deliveryRow.alignChildren = 'left';

    var deliveryNormal = deliveryRow.add('radiobutton', undefined, 'عادي (x1.0)');
    var deliveryFast = deliveryRow.add('radiobutton', undefined, 'سريع (x1.3)');
    var deliveryUrgent = deliveryRow.add('radiobutton', undefined, 'مستعجل (x1.5)');
    var deliveryExpress = deliveryRow.add('radiobutton', undefined, 'فوري (x2.0)');
    deliveryNormal.value = true;

    var deliveryHint = deliveryGroup.add('statictext', undefined, '⚡ التسليم المستعجل يتطلب رسوم إضافية');
    deliveryHint.graphics.font = ScriptUI.newFont(deliveryHint.graphics.font.name, ScriptUI.FontStyle.ITALIC, 10);
    
    // مجموعة النتيجة
    var resultGroup = dialog.add('panel', undefined, '💰 السعر المقترح');
    resultGroup.orientation = 'column';
    resultGroup.alignChildren = 'fill';
    resultGroup.margins = 15;

    // عرض السعر الرئيسي
    var priceDisplayGroup = resultGroup.add('group');
    priceDisplayGroup.orientation = 'row';
    priceDisplayGroup.alignChildren = 'center';

    var priceDisplay = priceDisplayGroup.add('statictext', undefined, 'اضغط "احسب السعر" للحصول على التقدير');
    priceDisplay.graphics.font = ScriptUI.newFont(priceDisplay.graphics.font.name, ScriptUI.FontStyle.BOLD, 16);
    priceDisplay.justify = 'center';

    // عرض التفاصيل
    var detailsDisplay = resultGroup.add('statictext', undefined, '', {multiline: true});
    detailsDisplay.preferredSize.height = 120;
    detailsDisplay.preferredSize.width = 450;
    detailsDisplay.graphics.font = ScriptUI.newFont(detailsDisplay.graphics.font.name, ScriptUI.FontStyle.REGULAR, 11);

    // شريط فاصل
    var separator = dialog.add('panel');
    separator.preferredSize.height = 2;

    // أزرار التحكم
    var buttonGroup = dialog.add('group');
    buttonGroup.orientation = 'row';
    buttonGroup.alignChildren = 'center';
    buttonGroup.spacing = 15;

    var calculateButton = buttonGroup.add('button', undefined, '🧮 احسب السعر');
    calculateButton.preferredSize.width = 120;
    calculateButton.preferredSize.height = 35;

    var saveButton = buttonGroup.add('button', undefined, '💾 حفظ التقدير');
    saveButton.preferredSize.width = 120;
    saveButton.preferredSize.height = 35;

    var exportButton = buttonGroup.add('button', undefined, '📁 تصدير متقدم');
    exportButton.preferredSize.width = 120;
    exportButton.preferredSize.height = 35;

    var resetButton = buttonGroup.add('button', undefined, '🔄 إعادة تعيين');
    resetButton.preferredSize.width = 120;
    resetButton.preferredSize.height = 35;

    var cancelButton = buttonGroup.add('button', undefined, '❌ إلغاء');
    cancelButton.preferredSize.width = 100;
    cancelButton.preferredSize.height = 35;
    
    // إرجاع كائن يحتوي على جميع عناصر الواجهة
    return {
        dialog: dialog,
        designTypeDropdown: designTypeDropdown,
        designDescription: designDescription,
        timeInput: timeInput,
        complexitySlider: complexitySlider,
        complexityValue: complexityValue,
        revisionsInput: revisionsInput,
        usagePersonal: usagePersonal,
        usageSmallBusiness: usageSmallBusiness,
        usageCommercial: usageCommercial,
        usageEnterprise: usageEnterprise,
        deliveryNormal: deliveryNormal,
        deliveryFast: deliveryFast,
        deliveryUrgent: deliveryUrgent,
        deliveryExpress: deliveryExpress,
        priceDisplay: priceDisplay,
        detailsDisplay: detailsDisplay,
        calculateButton: calculateButton,
        saveButton: saveButton,
        exportButton: exportButton,
        resetButton: resetButton,
        cancelButton: cancelButton
    };
}

// دالة حساب السعر المحسنة
function calculatePrice(ui) {
    try {
        // التحقق من صحة البيانات
        if (!ui.designTypeDropdown.selection) {
            alert('يرجى اختيار نوع التصميم');
            return 0;
        }

        // الحصول على القيم من الواجهة
        var designTypeIndex = ui.designTypeDropdown.selection.index;
        var designTypeKeys = Object.keys(PricingConfig.designTypes);
        var designType = PricingConfig.designTypes[designTypeKeys[designTypeIndex]];

        var hours = parseFloat(ui.timeInput.text) || 0;
        if (hours <= 0) {
            alert('يرجى إدخال عدد ساعات صحيح');
            return 0;
        }

        var complexityLevel = Math.round(ui.complexitySlider.value);
        var revisions = Math.max(0, parseInt(ui.revisionsInput.text) || 0);

        // تحديد نوع الاستخدام
        var usageType = 'personal';
        if (ui.usageSmallBusiness.value) usageType = 'small_business';
        else if (ui.usageCommercial.value) usageType = 'commercial';
        else if (ui.usageEnterprise.value) usageType = 'enterprise';

        // تحديد سرعة التسليم
        var deliveryType = 'normal';
        if (ui.deliveryFast.value) deliveryType = 'fast';
        else if (ui.deliveryUrgent.value) deliveryType = 'urgent';
        else if (ui.deliveryExpress.value) deliveryType = 'express';

        // حساب السعر الأساسي
        var basePrice = designType.basePrice * hours;

        // تطبيق معامل التعقيد
        var complexityKeys = Object.keys(PricingConfig.complexityMultipliers);
        var complexityData = PricingConfig.complexityMultipliers[complexityKeys[complexityLevel - 1]];
        var complexityMultiplier = complexityData.multiplier;

        // تطبيق معامل نوع الاستخدام
        var usageData = PricingConfig.usageMultipliers[usageType];
        var usageMultiplier = usageData.multiplier;

        // تطبيق معامل سرعة التسليم
        var deliveryData = PricingConfig.deliveryMultipliers[deliveryType];
        var deliveryMultiplier = deliveryData.multiplier;

        // حساب تكلفة التعديلات (فقط التعديلات الإضافية)
        var freeRevisions = 2; // عدد التعديلات المجانية
        var paidRevisions = Math.max(0, revisions - freeRevisions);
        var revisionsCost = paidRevisions * PricingConfig.revisionCost;

        // حساب السعر قبل التعديلات
        var priceBeforeRevisions = basePrice * complexityMultiplier * usageMultiplier * deliveryMultiplier;

        // حساب السعر النهائي
        var finalPrice = priceBeforeRevisions + revisionsCost;

        // تطبيق الحد الأدنى والأقصى
        finalPrice = Math.max(PricingConfig.settings.minProjectValue, finalPrice);
        finalPrice = Math.min(PricingConfig.settings.maxProjectValue, finalPrice);

        // التأكد من أن السعر رقم صحيح
        if (isNaN(finalPrice) || finalPrice <= 0) {
            finalPrice = 0;
            ui.priceDisplay.text = '❌ خطأ في الحساب - تحقق من البيانات';
            alert('❌ خطأ في حساب السعر - تحقق من البيانات المدخلة');
            return 0;
        }

        // تحديث العرض
        ui.priceDisplay.text = '💰 السعر المقترح: $' + Math.round(finalPrice);

        // عرض التفاصيل المفصلة
        var details = '📊 تفاصيل حساب السعر:\n\n';
        details += '🎨 نوع التصميم: ' + designType.name + ' ($' + designType.basePrice + '/ساعة)\n';
        details += '⏰ عدد الساعات: ' + hours + ' ساعة\n';
        details += '💵 السعر الأساسي: $' + Math.round(basePrice) + '\n\n';

        details += '🔧 معامل التعقيد: ' + complexityData.name + ' (x' + complexityMultiplier + ')\n';
        details += '👥 معامل الاستخدام: ' + usageData.name + ' (x' + usageMultiplier + ')\n';
        details += '🚀 معامل التسليم: ' + deliveryData.name + ' (x' + deliveryMultiplier + ')\n\n';

        details += '💰 السعر بعد المعاملات: $' + Math.round(priceBeforeRevisions) + '\n';

        if (revisions > freeRevisions) {
            details += '✏️ تعديلات إضافية: ' + paidRevisions + ' × $' + PricingConfig.revisionCost + ' = $' + revisionsCost + '\n';
        } else {
            details += '✏️ التعديلات: ' + revisions + ' (مشمولة مجاناً)\n';
        }

        details += '\n🎯 السعر النهائي: $' + Math.round(finalPrice);

        ui.detailsDisplay.text = details;

        return finalPrice;

    } catch (error) {
        alert('خطأ في حساب السعر: ' + error.message);
        return 0;
    }
}

// دالة حفظ التقدير المحسنة
function saveEstimate(ui, price) {
    try {
        var file = File.saveDialog('حفظ تقدير السعر', '*.txt');
        if (file) {
            file.open('w');
            file.encoding = 'UTF-8';

            // رأس الملف
            file.writeln('═══════════════════════════════════════════════════════════');
            file.writeln('                    تقدير سعر التصميم                    ');
            file.writeln('                  Design Price Estimate                  ');
            file.writeln('═══════════════════════════════════════════════════════════');
            file.writeln('');

            // معلومات التاريخ والوقت
            var now = new Date();
            file.writeln('📅 التاريخ: ' + now.toLocaleDateString());
            file.writeln('🕐 الوقت: ' + now.toLocaleTimeString());
            file.writeln('');

            // معلومات المشروع
            file.writeln('📋 معلومات المشروع:');
            file.writeln('───────────────────────────────────────────────────────────');

            var designTypeIndex = ui.designTypeDropdown.selection.index;
            var designTypeKeys = Object.keys(PricingConfig.designTypes);
            var designType = PricingConfig.designTypes[designTypeKeys[designTypeIndex]];

            file.writeln('🎨 نوع التصميم: ' + designType.name);
            file.writeln('⏰ عدد الساعات: ' + ui.timeInput.text);
            file.writeln('🔧 درجة التعقيد: ' + ui.complexityValue.text);
            file.writeln('✏️ عدد التعديلات: ' + ui.revisionsInput.text);

            // نوع الاستخدام
            var usageType = 'شخصي';
            if (ui.usageSmallBusiness.value) usageType = 'مشروع صغير';
            else if (ui.usageCommercial.value) usageType = 'تجاري';
            else if (ui.usageEnterprise.value) usageType = 'مؤسسي';
            file.writeln('👥 نوع الاستخدام: ' + usageType);

            // سرعة التسليم
            var deliveryType = 'عادي';
            if (ui.deliveryFast.value) deliveryType = 'سريع';
            else if (ui.deliveryUrgent.value) deliveryType = 'مستعجل';
            else if (ui.deliveryExpress.value) deliveryType = 'فوري';
            file.writeln('🚀 سرعة التسليم: ' + deliveryType);

            file.writeln('');

            // تفاصيل الحساب
            file.writeln('💰 تفاصيل الحساب:');
            file.writeln('───────────────────────────────────────────────────────────');
            file.writeln(ui.detailsDisplay.text.replace(/\n/g, '\r\n'));
            file.writeln('');

            // السعر النهائي
            file.writeln('═══════════════════════════════════════════════════════════');
            file.writeln('🎯 السعر النهائي: $' + Math.round(price));
            file.writeln('═══════════════════════════════════════════════════════════');
            file.writeln('');

            // ملاحظات
            file.writeln('📝 ملاحظات مهمة:');
            file.writeln('• هذا التقدير قابل للتفاوض حسب متطلبات المشروع');
            file.writeln('• السعر لا يشمل الضرائب إن وجدت');
            file.writeln('• التعديلات الإضافية قد تتطلب رسوم إضافية');
            file.writeln('• يرجى مراجعة التفاصيل قبل بدء العمل');
            file.writeln('');

            // تذييل
            file.writeln('───────────────────────────────────────────────────────────');
            file.writeln('تم إنشاء هذا التقدير بواسطة حاسبة أسعار التصميم v1.0');
            file.writeln('Design Price Calculator - Professional Pricing Tool');
            file.writeln('───────────────────────────────────────────────────────────');

            file.close();

            alert('✅ تم حفظ التقدير بنجاح!\n\nالملف محفوظ في:\n' + file.fsName);
        }
    } catch (error) {
        alert('❌ خطأ في حفظ الملف: ' + error.message);
    }
}

// دالة إعادة تعيين النموذج
function resetForm(ui) {
    try {
        // إعادة تعيين القيم الافتراضية
        ui.designTypeDropdown.selection = 0;
        ui.timeInput.text = PricingConfig.settings.defaultHours.toString();
        ui.complexitySlider.value = 1;
        ui.complexitySlider.onChanging();
        ui.revisionsInput.text = PricingConfig.settings.defaultRevisions.toString();

        // إعادة تعيين أزرار الاختيار
        ui.usagePersonal.value = true;
        ui.usageSmallBusiness.value = false;
        ui.usageCommercial.value = false;
        ui.usageEnterprise.value = false;

        ui.deliveryNormal.value = true;
        ui.deliveryFast.value = false;
        ui.deliveryUrgent.value = false;
        ui.deliveryExpress.value = false;

        // مسح النتائج
        ui.priceDisplay.text = 'اضغط "احسب السعر" للحصول على التقدير';
        ui.detailsDisplay.text = '';

        // تحديث وصف نوع التصميم
        if (ui.designTypeDropdown.onChange) {
            ui.designTypeDropdown.onChange();
        }

        alert('✅ تم إعادة تعيين النموذج بنجاح');

    } catch (error) {
        alert('❌ خطأ في إعادة التعيين: ' + error.message);
    }
}

// دالة حفظ التقدير باللغة الإنجليزية
function saveEstimateEnglish(ui, price) {
    try {
        var file = File.saveDialog('Save Price Estimate - English', '*.txt');
        if (file) {
            file.open('w');
            file.encoding = 'UTF-8';

            // رأس الملف
            file.writeln('═══════════════════════════════════════════════════════════');
            file.writeln('                    Design Price Estimate                    ');
            file.writeln('                    تقدير سعر التصميم                    ');
            file.writeln('═══════════════════════════════════════════════════════════');
            file.writeln('');

            // معلومات التاريخ والوقت
            var now = new Date();
            file.writeln('📅 Date: ' + now.toLocaleDateString());
            file.writeln('🕐 Time: ' + now.toLocaleTimeString());
            file.writeln('');

            // معلومات المشروع
            file.writeln('📋 Project Information:');
            file.writeln('───────────────────────────────────────────────────────────');

            var designTypeIndex = ui.designTypeDropdown.selection.index;
            var designTypeKeys = Object.keys(PricingConfig.designTypes);
            var designType = PricingConfig.designTypes[designTypeKeys[designTypeIndex]];

            file.writeln('🎨 Design Type: ' + (designType.nameEn || designType.name));
            file.writeln('⏰ Hours: ' + ui.timeInput.text + ' hours');
            file.writeln('🔧 Complexity: ' + ui.complexityValue.text);
            file.writeln('✏️ Revisions: ' + ui.revisionsInput.text);

            // نوع الاستخدام
            var usageType = 'Personal';
            if (ui.usageSmallBusiness.value) usageType = 'Small Business';
            else if (ui.usageCommercial.value) usageType = 'Commercial';
            else if (ui.usageEnterprise.value) usageType = 'Enterprise';
            file.writeln('👥 Usage Type: ' + usageType);

            // سرعة التسليم
            var deliveryType = 'Normal';
            if (ui.deliveryFast.value) deliveryType = 'Fast';
            else if (ui.deliveryUrgent.value) deliveryType = 'Urgent';
            else if (ui.deliveryExpress.value) deliveryType = 'Express';
            file.writeln('🚀 Delivery Speed: ' + deliveryType);

            file.writeln('');

            // تفاصيل الحساب (ترجمة بسيطة)
            file.writeln('💰 Price Calculation Details:');
            file.writeln('───────────────────────────────────────────────────────────');
            var detailsEnglish = ui.detailsDisplay.text
                .replace(/📊 تفاصيل حساب السعر:/g, '📊 Price Calculation Details:')
                .replace(/🎨 نوع التصميم:/g, '🎨 Design Type:')
                .replace(/⏰ عدد الساعات:/g, '⏰ Hours:')
                .replace(/💵 السعر الأساسي:/g, '💵 Base Price:')
                .replace(/🔧 معامل التعقيد:/g, '🔧 Complexity Factor:')
                .replace(/👥 معامل الاستخدام:/g, '👥 Usage Factor:')
                .replace(/🚀 معامل التسليم:/g, '🚀 Delivery Factor:')
                .replace(/💰 السعر بعد المعاملات:/g, '💰 Price after factors:')
                .replace(/✏️ التعديلات:/g, '✏️ Revisions:')
                .replace(/مشمولة مجاناً/g, 'included for free')
                .replace(/🎯 السعر النهائي:/g, '🎯 Final Price:')
                .replace(/ساعة/g, 'hour');

            file.writeln(detailsEnglish.replace(/\n/g, '\r\n'));
            file.writeln('');

            // السعر النهائي
            file.writeln('═══════════════════════════════════════════════════════════');
            file.writeln('🎯 Final Price: $' + Math.round(price));
            file.writeln('═══════════════════════════════════════════════════════════');
            file.writeln('');

            // ملاحظات
            file.writeln('📝 Important Notes:');
            file.writeln('• This estimate is valid for 30 days from the issue date');
            file.writeln('• Price does not include local taxes if applicable');
            file.writeln('• Additional changes may require additional fees');
            file.writeln('• Please review details before starting work');
            file.writeln('• Ownership rights transfer to client upon full payment');
            file.writeln('');

            // تذييل
            file.writeln('───────────────────────────────────────────────────────────');
            file.writeln('Generated by Design Price Calculator v1.0');
            file.writeln('Professional Pricing Tool for Designers');
            file.writeln('───────────────────────────────────────────────────────────');

            file.close();

            alert('✅ English estimate saved successfully!\n\nFile saved at:\n' + file.fsName);
        }
    } catch (error) {
        alert('❌ Error saving English file: ' + error.message);
    }
}

// دالة التحقق من صحة البيانات
function validateInputs(ui) {
    var errors = [];

    // التحقق من نوع التصميم
    if (!ui.designTypeDropdown.selection) {
        errors.push('يرجى اختيار نوع التصميم');
    }

    // التحقق من عدد الساعات
    var hours = parseFloat(ui.timeInput.text);
    if (isNaN(hours) || hours <= 0) {
        errors.push('يرجى إدخال عدد ساعات صحيح (أكبر من 0)');
    } else if (hours > 100) {
        errors.push('عدد الساعات كبير جداً (الحد الأقصى 100 ساعة)');
    }

    // التحقق من عدد التعديلات
    var revisions = parseInt(ui.revisionsInput.text);
    if (isNaN(revisions) || revisions < 0) {
        errors.push('يرجى إدخال عدد تعديلات صحيح (0 أو أكثر)');
    } else if (revisions > 20) {
        errors.push('عدد التعديلات كبير جداً (الحد الأقصى 20 تعديل)');
    }

    return errors;
}

// الدالة الرئيسية المحسنة
function main() {
    try {
        var ui = createUI();
        var currentPrice = 0;

        // ربط أحداث الحساب
        ui.calculateButton.onClick = function() {
            var errors = validateInputs(ui);
            if (errors.length > 0) {
                alert('❌ يرجى تصحيح الأخطاء التالية:\n\n• ' + errors.join('\n• '));
                return;
            }

            currentPrice = calculatePrice(ui);
            if (currentPrice > 0) {
                ui.saveButton.enabled = true;
                ui.exportButton.enabled = true;
            }
        };

        // ربط أحداث الحفظ
        ui.saveButton.onClick = function() {
            if (currentPrice > 0) {
                saveEstimate(ui, currentPrice);
            } else {
                alert('⚠️ يرجى حساب السعر أولاً قبل الحفظ');
            }
        };

        // ربط أحداث التصدير المتقدم
        ui.exportButton.onClick = function() {
            if (currentPrice > 0) {
                if (typeof showExportOptions === 'function') {
                    showExportOptions(ui, currentPrice);
                } else {
                    // في حالة عدم توفر وظائف التصدير المتقدمة، استخدم التصدير البسيط
                    var result = confirm('هل تريد حفظ التقدير باللغة الإنجليزية؟\nDo you want to save the estimate in English?');
                    if (result) {
                        saveEstimateEnglish(ui, currentPrice);
                    } else {
                        saveEstimate(ui, currentPrice);
                    }
                }
            } else {
                alert('⚠️ يرجى حساب السعر أولاً قبل التصدير');
            }
        };

        // ربط أحداث إعادة التعيين
        ui.resetButton.onClick = function() {
            var result = confirm('هل أنت متأكد من إعادة تعيين جميع البيانات؟');
            if (result) {
                resetForm(ui);
                currentPrice = 0;
                ui.saveButton.enabled = false;
                ui.exportButton.enabled = false;
            }
        };

        // ربط أحداث الإلغاء
        ui.cancelButton.onClick = function() {
            ui.dialog.close();
        };

        // تعطيل أزرار الحفظ والتصدير في البداية
        ui.saveButton.enabled = false;
        ui.exportButton.enabled = false;

        // إضافة مساعدة سريعة
        ui.dialog.onShow = function() {
            // يمكن إضافة رسالة ترحيب هنا
        };

        // إضافة اختصارات لوحة المفاتيح
        ui.dialog.addEventListener('keydown', function(event) {
            if (event.keyName === 'Enter') {
                ui.calculateButton.onClick();
            } else if (event.keyName === 'Escape') {
                ui.cancelButton.onClick();
            }
        });

        // عرض الواجهة
        ui.dialog.show();

    } catch (error) {
        alert('❌ خطأ في تشغيل السكريبت: ' + error.message);
    }
}

// تشغيل السكريبت
main();
