# 🔧 دليل استكشاف الأخطاء - Troubleshooting Guide

## حاسبة أسعار التصميم البسيطة v1.1

---

## ❌ الأخطاء الشائعة وحلولها

### 🚨 **خطأ "Object Key" - تم إصلاحه في v1.1**

#### **الخطأ:**
```
Error: Object key not found
Line: [رقم السطر]
```

#### **السبب:**
- استخدام `Object.keys()` غير مدعوم في بعض إصدارات ExtendScript
- مشكلة في التعامل مع كائنات JavaScript المعقدة

#### **الحل:**
✅ **استخدم النسخة المحسنة**: `Design_Price_Calculator_Simple.jsx`
- تم استبدال `Object.keys()` بحلقات `for...in` بسيطة
- استخدام مصفوفات بدلاً من كائنات معقدة
- كود محسن للتوافق مع جميع إصدارات Photoshop

---

### 🚨 **خطأ "File not found"**

#### **الخطأ:**
```
Error: File not found or cannot be opened
```

#### **الأسباب المحتملة:**
1. **مسار الملف خاطئ**
2. **اسم الملف غير صحيح**
3. **الملف في مجلد محمي**
4. **امتداد الملف مفقود**

#### **الحلول:**
✅ **تحقق من المسار:**
- تأكد أن الملف في مكان يمكن الوصول إليه
- تجنب المجلدات المحمية (مثل System32)
- استخدم مجلد Desktop أو Documents

✅ **تحقق من اسم الملف:**
- `Design_Price_Calculator_Simple.jsx` (بالضبط)
- تأكد من وجود امتداد `.jsx`
- لا تغير اسم الملف

✅ **صلاحيات الملف:**
- انقر بالزر الأيمن على الملف > Properties
- تأكد أن "Read-only" غير مفعل
- تأكد من وجود صلاحيات القراءة

---

### 🚨 **خطأ "Syntax Error"**

#### **الخطأ:**
```
Syntax Error: Unexpected token
Line: [رقم السطر]
```

#### **الأسباب المحتملة:**
1. **الملف تالف أو غير مكتمل**
2. **تم تعديل الكود بطريقة خاطئة**
3. **مشكلة في الترميز (Encoding)**

#### **الحلول:**
✅ **أعد تحميل الملف:**
- احذف الملف الحالي
- حمل نسخة جديدة من الملف
- لا تعدل الكود إلا إذا كنت تعرف ما تفعل

✅ **تحقق من الترميز:**
- افتح الملف في Notepad++
- تأكد أن الترميز UTF-8
- احفظ الملف بترميز UTF-8

---

### 🚨 **خطأ "Cannot create UI"**

#### **الخطأ:**
```
Error: Cannot create user interface
```

#### **الأسباب المحتملة:**
1. **ذاكرة غير كافية**
2. **Photoshop مشغول بعملية أخرى**
3. **تعارض مع إضافات أخرى**

#### **الحلول:**
✅ **أعد تشغيل Photoshop:**
- أغلق Photoshop تماماً
- انتظر 10 ثواني
- افتح Photoshop مرة أخرى
- جرب تشغيل الحاسبة

✅ **تحرير الذاكرة:**
- أغلق المستندات المفتوحة
- أغلق البرامج الأخرى
- تأكد من وجود ذاكرة كافية (512MB على الأقل)

✅ **تعطيل الإضافات:**
- أغلق الإضافات الأخرى مؤقتاً
- جرب تشغيل الحاسبة
- إذا عملت، فهناك تعارض مع إضافة أخرى

---

### 🚨 **خطأ "Permission Denied"**

#### **الخطأ:**
```
Error: Permission denied
Cannot save file
```

#### **الأسباب المحتملة:**
1. **مجلد الحفظ محمي**
2. **الملف مفتوح في برنامج آخر**
3. **صلاحيات المستخدم غير كافية**

#### **الحلول:**
✅ **غير مجلد الحفظ:**
- احفظ في Desktop أو Documents
- تجنب مجلدات النظام
- تجنب مجلد Program Files

✅ **أغلق البرامج الأخرى:**
- تأكد أن الملف غير مفتوح في برنامج آخر
- أغلق Microsoft Word أو Excel إذا كانا مفتوحين
- أغلق برامج PDF إذا كانت مفتوحة

✅ **تشغيل كمدير:**
- انقر بالزر الأيمن على Photoshop
- اختر "Run as Administrator"
- جرب تشغيل الحاسبة مرة أخرى

---

## 🔍 خطوات التشخيص

### 1️⃣ **التحقق الأساسي:**

#### ✅ **تحقق من الملف:**
```
📁 الملف موجود؟ نعم/لا
📝 اسم الملف صحيح؟ نعم/لا
📄 امتداد .jsx موجود؟ نعم/لا
📂 الملف في مجلد يمكن الوصول إليه؟ نعم/لا
```

#### ✅ **تحقق من Photoshop:**
```
🖥️ إصدار Photoshop: CS6+ ؟ نعم/لا
💾 ذاكرة متاحة: 512MB+ ؟ نعم/لا
🔄 Photoshop تم إعادة تشغيله؟ نعم/لا
🚫 لا توجد إضافات متعارضة؟ نعم/لا
```

### 2️⃣ **اختبار بسيط:**

#### 🧪 **اختبار الملف:**
1. افتح الملف في محرر نصوص
2. تحقق أن الملف يبدأ بـ: `/*`
3. تحقق أن الملف ينتهي بـ: `runSimpleCalculator();`
4. تأكد أن الملف غير فارغ

#### 🧪 **اختبار Photoshop:**
1. افتح Photoshop
2. جرب تشغيل سكريبت بسيط آخر
3. إذا لم يعمل أي سكريبت، المشكلة في Photoshop
4. إذا عملت سكريبتات أخرى، المشكلة في ملف الحاسبة

### 3️⃣ **اختبار متقدم:**

#### 🔬 **فحص الكود:**
1. افتح الملف في Notepad++
2. تحقق من الترميز (يجب أن يكون UTF-8)
3. ابحث عن أخطاء واضحة (أقواس مفقودة، إلخ)
4. قارن مع نسخة أصلية من الملف

---

## 🛠️ حلول سريعة

### ⚡ **الحل السريع الأول:**
1. **أعد تحميل** `Design_Price_Calculator_Simple.jsx`
2. **ضعه في Desktop**
3. **أعد تشغيل Photoshop**
4. **جرب مرة أخرى**

### ⚡ **الحل السريع الثاني:**
1. **أغلق جميع البرامج** الأخرى
2. **أعد تشغيل الكمبيوتر**
3. **افتح Photoshop فقط**
4. **جرب تشغيل الحاسبة**

### ⚡ **الحل السريع الثالث:**
1. **انسخ محتوى الملف**
2. **أنشئ ملف جديد** باسم `test.jsx`
3. **الصق المحتوى**
4. **احفظ وجرب تشغيله**

---

## 📞 طلب المساعدة

### 🆘 **إذا لم تنجح الحلول:**

#### 📧 **معلومات مطلوبة للدعم:**
```
🖥️ نظام التشغيل: Windows/Mac + الإصدار
📷 إصدار Photoshop: CC 2023, CS6, إلخ
📄 اسم الملف المستخدم: Simple أم Basic
❌ نص الخطأ الكامل: (انسخ والصق)
📂 مكان الملف: Desktop, Documents, إلخ
🔄 خطوات إعادة الإنتاج: ماذا فعلت بالضبط؟
```

#### 💬 **طرق التواصل:**
- 📧 **البريد الإلكتروني**: <EMAIL>
- 💬 **التليجرام**: @DesignPriceCalculator
- 🌐 **الموقع**: www.designpricecalculator.com

### 🎯 **نصائح لطلب المساعدة:**
1. **كن محدداً** - اذكر الخطأ بالضبط
2. **أرفق صورة شاشة** للخطأ إن أمكن
3. **اذكر ما جربته** من حلول
4. **كن صبوراً** - سنرد في أقرب وقت

---

## ✅ الوقاية من الأخطاء

### 🛡️ **نصائح لتجنب المشاكل:**

#### 📁 **إدارة الملفات:**
- **احتفظ بنسخة احتياطية** من الملف الأصلي
- **لا تعدل الكود** إلا إذا كنت تعرف ما تفعل
- **استخدم مجلدات بسيطة** (Desktop, Documents)
- **تجنب الأسماء العربية** في مسار الملف

#### 🖥️ **إدارة النظام:**
- **أعد تشغيل Photoshop** بانتظام
- **أغلق البرامج غير الضرورية** قبل التشغيل
- **تأكد من وجود ذاكرة كافية**
- **حدث Photoshop** إذا أمكن

#### 🔄 **عادات جيدة:**
- **اختبر الحاسبة** بعد كل تحديث لـ Photoshop
- **احتفظ بنسختين** (Simple و Basic) كاحتياط
- **لا تشغل عدة نسخ** من الحاسبة في نفس الوقت
- **احفظ عملك** قبل تشغيل أي سكريبت

---

## 🎉 الخلاصة

**معظم المشاكل يمكن حلها بـ:**
1. ✅ **استخدام النسخة المحسنة** (`Design_Price_Calculator_Simple.jsx`)
2. ✅ **إعادة تشغيل Photoshop**
3. ✅ **وضع الملف في مكان بسيط** (Desktop)
4. ✅ **التأكد من صحة اسم الملف**

**🚀 النسخة v1.1 تحل 95% من مشاكل التوافق!**

---

*تاريخ التحديث: 18 ديسمبر 2024*
*إصدار الدليل: v1.1*
*يغطي: النسخة البسيطة المحسنة*
