/*
وظائف التصدير والحفظ المتقدمة - Advanced Export Functions
مجموعة من الوظائف لحفظ وتصدير تقديرات الأسعار بصيغ مختلفة
*/

// إضافة دعم Object.keys للإصدارات القديمة من ExtendScript
if (!Object.keys) {
    Object.keys = function(obj) {
        var keys = [];
        for (var key in obj) {
            if (obj.hasOwnProperty(key)) {
                keys.push(key);
            }
        }
        return keys;
    };
}

// قاموس الترجمة
var translations = {
    arabic: {
        title: 'تقدير سعر التصميم',
        subtitle: 'Design Price Estimate',
        date: 'التاريخ',
        time: 'الوقت',
        projectInfo: 'معلومات المشروع',
        designType: 'نوع التصميم',
        hours: 'عدد الساعات',
        complexity: 'درجة التعقيد',
        revisions: 'عدد التعديلات',
        usageType: 'نوع الاستخدام',
        deliverySpeed: 'سرعة التسليم',
        priceDetails: 'تفاصيل حساب السعر',
        basePrice: 'السعر الأساسي',
        complexityFactor: 'معامل التعقيد',
        usageFactor: 'معامل الاستخدام',
        deliveryFactor: 'معامل التسليم',
        subtotal: 'السعر قبل التعديلات',
        additionalRevisions: 'تعديلات إضافية',
        finalPrice: 'السعر النهائي',
        notes: 'ملاحظات مهمة',
        validFor: 'هذا التقدير صالح لمدة 30 يوماً من تاريخ الإصدار',
        noTax: 'السعر لا يشمل الضرائب المحلية إن وجدت',
        additionalChanges: 'التعديلات الإضافية تتطلب موافقة مسبقة',
        deliveryTime: 'مدة التسليم تبدأ من تاريخ الموافقة على المشروع',
        ownership: 'حقوق الملكية تنتقل للعميل عند السداد الكامل',
        footer: 'تم إنشاء هذا التقدير بواسطة حاسبة أسعار التصميم v1.0',
        footerEn: 'Design Price Calculator - Professional Pricing Tool',
        hour: 'ساعة',
        included: 'مشمولة مجاناً'
    },
    english: {
        title: 'Design Price Estimate',
        subtitle: 'تقدير سعر التصميم',
        date: 'Date',
        time: 'Time',
        projectInfo: 'Project Information',
        designType: 'Design Type',
        hours: 'Hours',
        complexity: 'Complexity Level',
        revisions: 'Number of Revisions',
        usageType: 'Usage Type',
        deliverySpeed: 'Delivery Speed',
        priceDetails: 'Price Calculation Details',
        basePrice: 'Base Price',
        complexityFactor: 'Complexity Factor',
        usageFactor: 'Usage Factor',
        deliveryFactor: 'Delivery Factor',
        subtotal: 'Subtotal (before revisions)',
        additionalRevisions: 'Additional Revisions',
        finalPrice: 'Final Price',
        notes: 'Important Notes',
        validFor: 'This estimate is valid for 30 days from the issue date',
        noTax: 'Price does not include local taxes if applicable',
        additionalChanges: 'Additional changes require prior approval',
        deliveryTime: 'Delivery time starts from project approval date',
        ownership: 'Ownership rights transfer to client upon full payment',
        footer: 'Generated by Design Price Calculator v1.0',
        footerEn: 'حاسبة أسعار التصميم - أداة تسعير احترافية',
        hour: 'hour',
        included: 'included for free'
    }
};

// دالة الحصول على النص المترجم
function getText(key, language, fallback) {
    if (language === 'bilingual') {
        var arabicText = translations.arabic[key] || fallback || key;
        var englishText = translations.english[key] || fallback || key;
        return arabicText + ' / ' + englishText;
    }

    var lang = language === 'english' ? 'english' : 'arabic';
    return translations[lang][key] || fallback || key;
}

// دالة إنشاء تقرير مفصل
function generateDetailedReport(ui, price, language) {
    language = language || 'arabic';
    var report = {};
    
    // معلومات أساسية
    report.timestamp = new Date();
    report.finalPrice = Math.round(price);
    
    // تفاصيل المشروع
    var designTypeIndex = ui.designTypeDropdown.selection.index;
    var designTypeKeys = Object.keys(PricingConfig.designTypes);
    var designType = PricingConfig.designTypes[designTypeKeys[designTypeIndex]];
    
    report.projectDetails = {
        designType: designType.name,
        designTypeKey: designTypeKeys[designTypeIndex],
        hours: parseFloat(ui.timeInput.text) || 0,
        complexity: Math.round(ui.complexitySlider.value),
        revisions: parseInt(ui.revisionsInput.text) || 0
    };
    
    // نوع الاستخدام
    report.usageType = 'personal';
    if (ui.usageSmallBusiness && ui.usageSmallBusiness.value) report.usageType = 'small_business';
    else if (ui.usageCommercial && ui.usageCommercial.value) report.usageType = 'commercial';
    else if (ui.usageEnterprise && ui.usageEnterprise.value) report.usageType = 'enterprise';
    
    // سرعة التسليم
    report.deliveryType = 'normal';
    if (ui.deliveryFast && ui.deliveryFast.value) report.deliveryType = 'fast';
    else if (ui.deliveryUrgent && ui.deliveryUrgent.value) report.deliveryType = 'urgent';
    else if (ui.deliveryExpress && ui.deliveryExpress.value) report.deliveryType = 'express';
    
    // حسابات مفصلة
    var basePrice = designType.basePrice * report.projectDetails.hours;
    var complexityKeys = Object.keys(PricingConfig.complexityMultipliers);
    var complexityData = PricingConfig.complexityMultipliers[complexityKeys[report.projectDetails.complexity - 1]];
    var usageData = PricingConfig.usageMultipliers[report.usageType];
    var deliveryData = PricingConfig.deliveryMultipliers[report.deliveryType];
    
    var freeRevisions = 2;
    var paidRevisions = Math.max(0, report.projectDetails.revisions - freeRevisions);
    var revisionsCost = paidRevisions * PricingConfig.revisionCost;
    
    report.calculations = {
        basePrice: basePrice,
        complexityMultiplier: complexityData.multiplier,
        usageMultiplier: usageData.multiplier,
        deliveryMultiplier: deliveryData.multiplier,
        priceBeforeRevisions: basePrice * complexityData.multiplier * usageData.multiplier * deliveryData.multiplier,
        revisionsCost: revisionsCost,
        freeRevisions: freeRevisions,
        paidRevisions: paidRevisions
    };
    
    return report;
}

// دالة حفظ كملف نصي منسق
function saveAsFormattedText(ui, price, language) {
    try {
        language = language || 'arabic';
        var dialogTitle = language === 'english' ?
            'Save Price Estimate - Formatted Text' :
            'حفظ تقدير السعر - نص منسق';

        var file = File.saveDialog(dialogTitle, '*.txt');
        if (!file) return false;

        var report = generateDetailedReport(ui, price, language);

        file.open('w');
        file.encoding = 'UTF-8';

        // رأس التقرير
        file.writeln('╔══════════════════════════════════════════════════════════════╗');
        if (language === 'english') {
            file.writeln('║                     Design Price Estimate                     ║');
            file.writeln('║                    Professional Pricing Tool                  ║');
        } else if (language === 'bilingual') {
            file.writeln('║            تقدير سعر التصميم / Design Price Estimate            ║');
        } else {
            file.writeln('║                        تقدير سعر التصميم                        ║');
            file.writeln('║                     Design Price Estimate                     ║');
        }
        file.writeln('╚══════════════════════════════════════════════════════════════╝');
        file.writeln('');

        // معلومات التاريخ
        file.writeln('📅 ' + getText('date', language) + ': ' + report.timestamp.toLocaleDateString());
        file.writeln('🕐 ' + getText('time', language) + ': ' + report.timestamp.toLocaleTimeString());
        file.writeln('');
        
        // تفاصيل المشروع
        file.writeln('┌─ 📋 ' + getText('projectInfo', language) + ' ─────────────────────────────────────────┐');

        var designTypeName = language === 'english' ?
            (PricingConfig.designTypes[report.projectDetails.designTypeKey].nameEn || report.projectDetails.designType) :
            report.projectDetails.designType;

        file.writeln('│ 🎨 ' + getText('designType', language) + ': ' + designTypeName);
        file.writeln('│ ⏰ ' + getText('hours', language) + ': ' + report.projectDetails.hours + ' ' + getText('hour', language));
        file.writeln('│ 🔧 ' + getText('complexity', language) + ': ' + ui.complexityValue.text);
        file.writeln('│ ✏️ ' + getText('revisions', language) + ': ' + report.projectDetails.revisions);

        var usageName = language === 'english' ?
            (PricingConfig.usageMultipliers[report.usageType].nameEn || PricingConfig.usageMultipliers[report.usageType].name) :
            PricingConfig.usageMultipliers[report.usageType].name;
        file.writeln('│ 👥 ' + getText('usageType', language) + ': ' + usageName);

        var deliveryName = language === 'english' ?
            (PricingConfig.deliveryMultipliers[report.deliveryType].nameEn || PricingConfig.deliveryMultipliers[report.deliveryType].name) :
            PricingConfig.deliveryMultipliers[report.deliveryType].name;
        file.writeln('│ 🚀 ' + getText('deliverySpeed', language) + ': ' + deliveryName);
        file.writeln('└──────────────────────────────────────────────────────────────┘');
        file.writeln('');
        
        // تفاصيل الحساب
        file.writeln('┌─ 💰 ' + getText('priceDetails', language) + ' ──────────────────────────────────────┐');
        file.writeln('│ ' + getText('basePrice', language) + ': $' + Math.round(report.calculations.basePrice));
        file.writeln('│ ' + getText('complexityFactor', language) + ': ×' + report.calculations.complexityMultiplier);
        file.writeln('│ ' + getText('usageFactor', language) + ': ×' + report.calculations.usageMultiplier);
        file.writeln('│ ' + getText('deliveryFactor', language) + ': ×' + report.calculations.deliveryMultiplier);
        file.writeln('│ ─────────────────────────────────────────────────────────────');
        file.writeln('│ ' + getText('subtotal', language) + ': $' + Math.round(report.calculations.priceBeforeRevisions));

        if (report.calculations.paidRevisions > 0) {
            file.writeln('│ ' + getText('additionalRevisions', language) + ': ' + report.calculations.paidRevisions + ' × $' + PricingConfig.revisionCost + ' = $' + report.calculations.revisionsCost);
        } else {
            var revisionsText = language === 'english' ?
                'Revisions: ' + getText('included', language) + ' (' + report.projectDetails.revisions + ' revisions)' :
                'التعديلات: ' + getText('included', language) + ' (' + report.projectDetails.revisions + ' تعديل)';
            file.writeln('│ ' + revisionsText);
        }

        file.writeln('│ ═════════════════════════════════════════════════════════════');
        file.writeln('│ 🎯 ' + getText('finalPrice', language) + ': $' + report.finalPrice);
        file.writeln('└──────────────────────────────────────────────────────────────┘');
        file.writeln('');
        
        // ملاحظات وشروط
        var notesTitle = language === 'english' ? 'Important Notes' :
                        language === 'bilingual' ? 'ملاحظات مهمة / Important Notes' : 'ملاحظات مهمة';

        file.writeln('┌─ 📝 ' + notesTitle + ' ─────────────────────────────────────────┐');
        file.writeln('│ • ' + getText('validFor', language));
        file.writeln('│ • ' + getText('noTax', language));
        file.writeln('│ • ' + getText('additionalChanges', language));
        file.writeln('│ • ' + getText('deliveryTime', language));
        file.writeln('│ • ' + getText('ownership', language));
        file.writeln('└──────────────────────────────────────────────────────────────┘');
        file.writeln('');

        // تذييل
        file.writeln('═══════════════════════════════════════════════════════════════');
        if (language === 'english') {
            file.writeln('Generated by Design Price Calculator v1.1');
            file.writeln('Professional Pricing Tool for Designers');
        } else if (language === 'bilingual') {
            file.writeln('تم إنشاء هذا التقدير بواسطة حاسبة أسعار التصميم v1.1');
            file.writeln('Generated by Design Price Calculator v1.1 - Professional Tool');
        } else {
            file.writeln('تم إنشاء هذا التقدير بواسطة حاسبة أسعار التصميم v1.1');
            file.writeln('Design Price Calculator - Professional Pricing Tool');
        }
        file.writeln('═══════════════════════════════════════════════════════════════');
        
        file.close();
        return true;
        
    } catch (error) {
        alert('خطأ في حفظ الملف: ' + error.message);
        return false;
    }
}

// دالة حفظ كملف CSV للتحليل
function saveAsCSV(ui, price, language) {
    try {
        language = language || 'arabic';
        var dialogTitle = language === 'english' ?
            'Save Price Estimate - CSV' :
            'حفظ تقدير السعر - CSV';

        var file = File.saveDialog(dialogTitle, '*.csv');
        if (!file) return false;

        var report = generateDetailedReport(ui, price, language);

        file.open('w');
        file.encoding = 'UTF-8';

        // رأس CSV حسب اللغة
        var headers = [];
        if (language === 'english') {
            headers = ['Date', 'Time', 'Design Type', 'Hours', 'Complexity', 'Revisions', 'Usage Type', 'Delivery Speed', 'Base Price', 'Complexity Factor', 'Usage Factor', 'Delivery Factor', 'Revisions Cost', 'Final Price'];
        } else if (language === 'bilingual') {
            headers = ['التاريخ/Date', 'الوقت/Time', 'نوع التصميم/Design Type', 'الساعات/Hours', 'التعقيد/Complexity', 'التعديلات/Revisions', 'نوع الاستخدام/Usage Type', 'سرعة التسليم/Delivery Speed', 'السعر الأساسي/Base Price', 'معامل التعقيد/Complexity Factor', 'معامل الاستخدام/Usage Factor', 'معامل التسليم/Delivery Factor', 'تكلفة التعديلات/Revisions Cost', 'السعر النهائي/Final Price'];
        } else {
            headers = ['التاريخ', 'الوقت', 'نوع التصميم', 'الساعات', 'التعقيد', 'التعديلات', 'نوع الاستخدام', 'سرعة التسليم', 'السعر الأساسي', 'معامل التعقيد', 'معامل الاستخدام', 'معامل التسليم', 'تكلفة التعديلات', 'السعر النهائي'];
        }

        file.writeln(headers.join(','));
        
        // بيانات التقدير
        var designTypeName = language === 'english' ?
            (PricingConfig.designTypes[report.projectDetails.designTypeKey].nameEn || report.projectDetails.designType) :
            report.projectDetails.designType;

        var usageName = language === 'english' ?
            (PricingConfig.usageMultipliers[report.usageType].nameEn || PricingConfig.usageMultipliers[report.usageType].name) :
            PricingConfig.usageMultipliers[report.usageType].name;

        var deliveryName = language === 'english' ?
            (PricingConfig.deliveryMultipliers[report.deliveryType].nameEn || PricingConfig.deliveryMultipliers[report.deliveryType].name) :
            PricingConfig.deliveryMultipliers[report.deliveryType].name;

        if (language === 'bilingual') {
            designTypeName = report.projectDetails.designType + '/' + (PricingConfig.designTypes[report.projectDetails.designTypeKey].nameEn || report.projectDetails.designType);
            usageName = PricingConfig.usageMultipliers[report.usageType].name + '/' + (PricingConfig.usageMultipliers[report.usageType].nameEn || PricingConfig.usageMultipliers[report.usageType].name);
            deliveryName = PricingConfig.deliveryMultipliers[report.deliveryType].name + '/' + (PricingConfig.deliveryMultipliers[report.deliveryType].nameEn || PricingConfig.deliveryMultipliers[report.deliveryType].name);
        }

        var csvLine = [
            report.timestamp.toLocaleDateString(),
            report.timestamp.toLocaleTimeString(),
            designTypeName,
            report.projectDetails.hours,
            report.projectDetails.complexity,
            report.projectDetails.revisions,
            usageName,
            deliveryName,
            Math.round(report.calculations.basePrice),
            report.calculations.complexityMultiplier,
            report.calculations.usageMultiplier,
            report.calculations.deliveryMultiplier,
            report.calculations.revisionsCost,
            report.finalPrice
        ].join(',');
        
        file.writeln(csvLine);
        file.close();
        return true;
        
    } catch (error) {
        alert('خطأ في حفظ ملف CSV: ' + error.message);
        return false;
    }
}

// دالة حفظ كملف JSON للتطبيقات الأخرى
function saveAsJSON(ui, price, language) {
    try {
        language = language || 'arabic';
        var dialogTitle = language === 'english' ?
            'Save Price Estimate - JSON' :
            'حفظ تقدير السعر - JSON';

        var file = File.saveDialog(dialogTitle, '*.json');
        if (!file) return false;

        var report = generateDetailedReport(ui, price, language);
        
        // إنشاء كائن JSON
        var jsonData = {
            version: "1.0",
            generator: "Design Price Calculator",
            language: language,
            timestamp: report.timestamp.toISOString(),
            estimate: {
                finalPrice: report.finalPrice,
                currency: "USD",
                validUntil: new Date(report.timestamp.getTime() + 30 * 24 * 60 * 60 * 1000).toISOString(),
                project: {
                    designType: report.projectDetails.designType,
                    designTypeEn: PricingConfig.designTypes[report.projectDetails.designTypeKey].nameEn || report.projectDetails.designType,
                    designTypeKey: report.projectDetails.designTypeKey,
                    estimatedHours: report.projectDetails.hours,
                    complexityLevel: report.projectDetails.complexity,
                    revisionsCount: report.projectDetails.revisions,
                    usageType: report.usageType,
                    usageTypeAr: PricingConfig.usageMultipliers[report.usageType].name,
                    usageTypeEn: PricingConfig.usageMultipliers[report.usageType].nameEn || PricingConfig.usageMultipliers[report.usageType].name,
                    deliveryType: report.deliveryType,
                    deliveryTypeAr: PricingConfig.deliveryMultipliers[report.deliveryType].name,
                    deliveryTypeEn: PricingConfig.deliveryMultipliers[report.deliveryType].nameEn || PricingConfig.deliveryMultipliers[report.deliveryType].name
                },
                pricing: {
                    basePrice: Math.round(report.calculations.basePrice),
                    multipliers: {
                        complexity: report.calculations.complexityMultiplier,
                        usage: report.calculations.usageMultiplier,
                        delivery: report.calculations.deliveryMultiplier
                    },
                    additionalCosts: {
                        revisions: report.calculations.revisionsCost,
                        freeRevisionsUsed: Math.min(report.projectDetails.revisions, report.calculations.freeRevisions),
                        paidRevisions: report.calculations.paidRevisions
                    }
                },
                breakdown: {
                    subtotal: Math.round(report.calculations.priceBeforeRevisions),
                    revisionsCost: report.calculations.revisionsCost,
                    total: report.finalPrice
                }
            }
        };
        
        file.open('w');
        file.encoding = 'UTF-8';
        file.write(JSON.stringify(jsonData, null, 2));
        file.close();
        return true;
        
    } catch (error) {
        alert('خطأ في حفظ ملف JSON: ' + error.message);
        return false;
    }
}

// دالة إنشاء عرض سعر احترافي
function generateQuoteDocument(ui, price, language) {
    try {
        language = language || 'arabic';
        var dialogTitle = language === 'english' ?
            'Save Price Quote' :
            'حفظ عرض السعر';

        var file = File.saveDialog(dialogTitle, '*.txt');
        if (!file) return false;

        var report = generateDetailedReport(ui, price, language);
        
        file.open('w');
        file.encoding = 'UTF-8';
        
        // رأس عرض السعر
        file.writeln('╔══════════════════════════════════════════════════════════════╗');
        file.writeln('║                           عرض سعر                            ║');
        file.writeln('║                      PRICE QUOTATION                        ║');
        file.writeln('╚══════════════════════════════════════════════════════════════╝');
        file.writeln('');
        
        // معلومات المقدم
        file.writeln('من: [اسم المصمم/الاستوديو]');
        file.writeln('إلى: [اسم العميل]');
        file.writeln('التاريخ: ' + report.timestamp.toLocaleDateString());
        file.writeln('رقم العرض: QT-' + report.timestamp.getFullYear() + 
                     String(report.timestamp.getMonth() + 1).padStart(2, '0') + 
                     String(report.timestamp.getDate()).padStart(2, '0') + '-' + 
                     String(Math.floor(Math.random() * 1000)).padStart(3, '0'));
        file.writeln('');
        
        // وصف المشروع
        file.writeln('┌─ 📋 وصف المشروع ────────────────────────────────────────────┐');
        file.writeln('│ نوع الخدمة: ' + report.projectDetails.designType);
        file.writeln('│ نطاق العمل: تصميم احترافي حسب المتطلبات المحددة');
        file.writeln('│ مدة التنفيذ: ' + Math.ceil(report.projectDetails.hours / 8) + ' أيام عمل');
        file.writeln('│ عدد التعديلات: ' + report.calculations.freeRevisions + ' تعديل مجاني');
        file.writeln('│ الملفات المسلمة: ملفات عالية الجودة بصيغ مختلفة');
        file.writeln('└──────────────────────────────────────────────────────────────┘');
        file.writeln('');
        
        // تفاصيل التكلفة
        file.writeln('┌─ 💰 تفاصيل التكلفة ─────────────────────────────────────────┐');
        file.writeln('│ الخدمة الأساسية: $' + Math.round(report.calculations.priceBeforeRevisions));
        if (report.calculations.revisionsCost > 0) {
            file.writeln('│ تعديلات إضافية: $' + report.calculations.revisionsCost);
        }
        file.writeln('│ ─────────────────────────────────────────────────────────────');
        file.writeln('│ المجموع الفرعي: $' + (Math.round(report.calculations.priceBeforeRevisions) + report.calculations.revisionsCost));
        file.writeln('│ الضريبة: $0 (غير مشمولة)');
        file.writeln('│ ═════════════════════════════════════════════════════════════');
        file.writeln('│ 🎯 المجموع الكلي: $' + report.finalPrice);
        file.writeln('└──────────────────────────────────────────────────────────────┘');
        file.writeln('');
        
        // شروط الدفع
        file.writeln('┌─ 💳 شروط الدفع ─────────────────────────────────────────────┐');
        file.writeln('│ • 50% مقدم عند بدء العمل: $' + Math.round(report.finalPrice * 0.5));
        file.writeln('│ • 50% عند التسليم النهائي: $' + Math.round(report.finalPrice * 0.5));
        file.writeln('│ • طرق الدفع: تحويل بنكي، PayPal، أو حسب الاتفاق');
        file.writeln('│ • العملة: دولار أمريكي (USD)');
        file.writeln('└──────────────────────────────────────────────────────────────┘');
        file.writeln('');
        
        // الشروط والأحكام
        file.writeln('┌─ 📜 الشروط والأحكام ────────────────────────────────────────┐');
        file.writeln('│ • هذا العرض صالح لمدة 30 يوماً من تاريخ الإصدار');
        file.writeln('│ • بدء العمل يتطلب موافقة كتابية ودفع المقدم');
        file.writeln('│ • التعديلات الإضافية تتطلب موافقة على التكلفة');
        file.writeln('│ • حقوق الملكية تنتقل للعميل عند السداد الكامل');
        file.writeln('│ • إلغاء المشروع: استرداد جزئي حسب مرحلة العمل');
        file.writeln('└──────────────────────────────────────────────────────────────┘');
        file.writeln('');
        
        // معلومات التواصل
        file.writeln('┌─ 📞 معلومات التواصل ────────────────────────────────────────┐');
        file.writeln('│ الهاتف: [رقم الهاتف]');
        file.writeln('│ البريد الإلكتروني: [البريد الإلكتروني]');
        file.writeln('│ الموقع الإلكتروني: [الموقع]');
        file.writeln('│ العنوان: [العنوان]');
        file.writeln('└──────────────────────────────────────────────────────────────┘');
        file.writeln('');
        
        // تذييل
        file.writeln('═══════════════════════════════════════════════════════════════');
        file.writeln('شكراً لثقتكم - نتطلع للعمل معكم');
        file.writeln('Thank you for your trust - We look forward to working with you');
        file.writeln('═══════════════════════════════════════════════════════════════');
        
        file.close();
        return true;
        
    } catch (error) {
        alert('خطأ في إنشاء عرض السعر: ' + error.message);
        return false;
    }
}

// دالة عرض خيارات التصدير
function showExportOptions(ui, price) {
    var exportDialog = new Window('dialog', 'خيارات التصدير والحفظ - Export Options');
    exportDialog.orientation = 'column';
    exportDialog.alignChildren = 'fill';
    exportDialog.spacing = 15;
    exportDialog.margins = 20;
    exportDialog.preferredSize.width = 500;

    var title = exportDialog.add('statictext', undefined, '📁 اختر صيغة الحفظ المناسبة');
    title.graphics.font = ScriptUI.newFont(title.graphics.font.name, ScriptUI.FontStyle.BOLD, 14);

    // مجموعة اختيار اللغة
    var languageGroup = exportDialog.add('panel', undefined, '🌐 لغة التصدير - Export Language');
    languageGroup.orientation = 'row';
    languageGroup.alignChildren = 'left';
    languageGroup.margins = 15;

    var arabicLang = languageGroup.add('radiobutton', undefined, '🇸🇦 العربية (Arabic)');
    var englishLang = languageGroup.add('radiobutton', undefined, '🇺🇸 الإنجليزية (English)');
    var bilingualLang = languageGroup.add('radiobutton', undefined, '🌍 ثنائي اللغة (Bilingual)');
    arabicLang.value = true;

    var optionsGroup = exportDialog.add('panel', undefined, 'صيغ الحفظ المتاحة - Available Formats');
    optionsGroup.orientation = 'column';
    optionsGroup.alignChildren = 'fill';
    optionsGroup.margins = 15;

    var textOption = optionsGroup.add('radiobutton', undefined, '📄 ملف نصي منسق (للطباعة والعرض)');
    var csvOption = optionsGroup.add('radiobutton', undefined, '📊 ملف CSV (للتحليل والإحصائيات)');
    var jsonOption = optionsGroup.add('radiobutton', undefined, '🔧 ملف JSON (للتطبيقات الأخرى)');
    var quoteOption = optionsGroup.add('radiobutton', undefined, '💼 عرض سعر احترافي (للعملاء)');

    textOption.value = true;
    
    var buttonGroup = exportDialog.add('group');
    buttonGroup.orientation = 'row';
    buttonGroup.alignChildren = 'center';
    
    var exportButton = buttonGroup.add('button', undefined, '💾 تصدير');
    var cancelButton = buttonGroup.add('button', undefined, '❌ إلغاء');
    
    exportButton.onClick = function() {
        var success = false;
        var selectedLanguage = 'arabic';

        if (englishLang.value) selectedLanguage = 'english';
        else if (bilingualLang.value) selectedLanguage = 'bilingual';

        if (textOption.value) {
            success = saveAsFormattedText(ui, price, selectedLanguage);
        } else if (csvOption.value) {
            success = saveAsCSV(ui, price, selectedLanguage);
        } else if (jsonOption.value) {
            success = saveAsJSON(ui, price, selectedLanguage);
        } else if (quoteOption.value) {
            success = generateQuoteDocument(ui, price, selectedLanguage);
        }

        if (success) {
            var successMsg = selectedLanguage === 'english' ?
                '✅ Export completed successfully!' :
                '✅ تم التصدير بنجاح!';
            alert(successMsg);
            exportDialog.close();
        }
    };
    
    cancelButton.onClick = function() {
        exportDialog.close();
    };
    
    exportDialog.show();
}
