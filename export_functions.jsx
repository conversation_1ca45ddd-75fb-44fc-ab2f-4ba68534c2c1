/*
وظائف التصدير والحفظ المتقدمة - Advanced Export Functions
مجموعة من الوظائف لحفظ وتصدير تقديرات الأسعار بصيغ مختلفة
*/

// دالة إنشاء تقرير مفصل
function generateDetailedReport(ui, price) {
    var report = {};
    
    // معلومات أساسية
    report.timestamp = new Date();
    report.finalPrice = Math.round(price);
    
    // تفاصيل المشروع
    var designTypeIndex = ui.designTypeDropdown.selection.index;
    var designTypeKeys = Object.keys(PricingConfig.designTypes);
    var designType = PricingConfig.designTypes[designTypeKeys[designTypeIndex]];
    
    report.projectDetails = {
        designType: designType.name,
        designTypeKey: designTypeKeys[designTypeIndex],
        hours: parseFloat(ui.timeInput.text) || 0,
        complexity: Math.round(ui.complexitySlider.value),
        revisions: parseInt(ui.revisionsInput.text) || 0
    };
    
    // نوع الاستخدام
    report.usageType = 'personal';
    if (ui.usageSmallBusiness && ui.usageSmallBusiness.value) report.usageType = 'small_business';
    else if (ui.usageCommercial && ui.usageCommercial.value) report.usageType = 'commercial';
    else if (ui.usageEnterprise && ui.usageEnterprise.value) report.usageType = 'enterprise';
    
    // سرعة التسليم
    report.deliveryType = 'normal';
    if (ui.deliveryFast && ui.deliveryFast.value) report.deliveryType = 'fast';
    else if (ui.deliveryUrgent && ui.deliveryUrgent.value) report.deliveryType = 'urgent';
    else if (ui.deliveryExpress && ui.deliveryExpress.value) report.deliveryType = 'express';
    
    // حسابات مفصلة
    var basePrice = designType.basePrice * report.projectDetails.hours;
    var complexityKeys = Object.keys(PricingConfig.complexityMultipliers);
    var complexityData = PricingConfig.complexityMultipliers[complexityKeys[report.projectDetails.complexity - 1]];
    var usageData = PricingConfig.usageMultipliers[report.usageType];
    var deliveryData = PricingConfig.deliveryMultipliers[report.deliveryType];
    
    var freeRevisions = 2;
    var paidRevisions = Math.max(0, report.projectDetails.revisions - freeRevisions);
    var revisionsCost = paidRevisions * PricingConfig.revisionCost;
    
    report.calculations = {
        basePrice: basePrice,
        complexityMultiplier: complexityData.multiplier,
        usageMultiplier: usageData.multiplier,
        deliveryMultiplier: deliveryData.multiplier,
        priceBeforeRevisions: basePrice * complexityData.multiplier * usageData.multiplier * deliveryData.multiplier,
        revisionsCost: revisionsCost,
        freeRevisions: freeRevisions,
        paidRevisions: paidRevisions
    };
    
    return report;
}

// دالة حفظ كملف نصي منسق
function saveAsFormattedText(ui, price) {
    try {
        var file = File.saveDialog('حفظ تقدير السعر - نص منسق', '*.txt');
        if (!file) return false;
        
        var report = generateDetailedReport(ui, price);
        
        file.open('w');
        file.encoding = 'UTF-8';
        
        // رأس التقرير
        file.writeln('╔══════════════════════════════════════════════════════════════╗');
        file.writeln('║                        تقدير سعر التصميم                        ║');
        file.writeln('║                     Design Price Estimate                     ║');
        file.writeln('╚══════════════════════════════════════════════════════════════╝');
        file.writeln('');
        
        // معلومات التاريخ
        file.writeln('📅 تاريخ التقدير: ' + report.timestamp.toLocaleDateString());
        file.writeln('🕐 وقت التقدير: ' + report.timestamp.toLocaleTimeString());
        file.writeln('');
        
        // تفاصيل المشروع
        file.writeln('┌─ 📋 تفاصيل المشروع ─────────────────────────────────────────┐');
        file.writeln('│ 🎨 نوع التصميم: ' + report.projectDetails.designType);
        file.writeln('│ ⏰ عدد الساعات: ' + report.projectDetails.hours + ' ساعة');
        file.writeln('│ 🔧 درجة التعقيد: ' + ui.complexityValue.text);
        file.writeln('│ ✏️ عدد التعديلات: ' + report.projectDetails.revisions);
        file.writeln('│ 👥 نوع الاستخدام: ' + PricingConfig.usageMultipliers[report.usageType].name);
        file.writeln('│ 🚀 سرعة التسليم: ' + PricingConfig.deliveryMultipliers[report.deliveryType].name);
        file.writeln('└──────────────────────────────────────────────────────────────┘');
        file.writeln('');
        
        // تفاصيل الحساب
        file.writeln('┌─ 💰 تفاصيل حساب السعر ──────────────────────────────────────┐');
        file.writeln('│ السعر الأساسي: $' + Math.round(report.calculations.basePrice));
        file.writeln('│ معامل التعقيد: ×' + report.calculations.complexityMultiplier);
        file.writeln('│ معامل الاستخدام: ×' + report.calculations.usageMultiplier);
        file.writeln('│ معامل التسليم: ×' + report.calculations.deliveryMultiplier);
        file.writeln('│ ─────────────────────────────────────────────────────────────');
        file.writeln('│ السعر قبل التعديلات: $' + Math.round(report.calculations.priceBeforeRevisions));
        
        if (report.calculations.paidRevisions > 0) {
            file.writeln('│ تعديلات إضافية: ' + report.calculations.paidRevisions + ' × $' + PricingConfig.revisionCost + ' = $' + report.calculations.revisionsCost);
        } else {
            file.writeln('│ التعديلات: مشمولة مجاناً (' + report.projectDetails.revisions + ' تعديل)');
        }
        
        file.writeln('│ ═════════════════════════════════════════════════════════════');
        file.writeln('│ 🎯 السعر النهائي: $' + report.finalPrice);
        file.writeln('└──────────────────────────────────────────────────────────────┘');
        file.writeln('');
        
        // ملاحظات وشروط
        file.writeln('┌─ 📝 ملاحظات وشروط ─────────────────────────────────────────┐');
        file.writeln('│ • هذا التقدير صالح لمدة 30 يوماً من تاريخ الإصدار');
        file.writeln('│ • السعر لا يشمل الضرائب المحلية إن وجدت');
        file.writeln('│ • التعديلات الإضافية تتطلب موافقة مسبقة');
        file.writeln('│ • مدة التسليم تبدأ من تاريخ الموافقة على المشروع');
        file.writeln('│ • حقوق الملكية تنتقل للعميل عند السداد الكامل');
        file.writeln('└──────────────────────────────────────────────────────────────┘');
        file.writeln('');
        
        // تذييل
        file.writeln('═══════════════════════════════════════════════════════════════');
        file.writeln('تم إنشاء هذا التقدير بواسطة حاسبة أسعار التصميم v1.0');
        file.writeln('Design Price Calculator - Professional Pricing Tool');
        file.writeln('═══════════════════════════════════════════════════════════════');
        
        file.close();
        return true;
        
    } catch (error) {
        alert('خطأ في حفظ الملف: ' + error.message);
        return false;
    }
}

// دالة حفظ كملف CSV للتحليل
function saveAsCSV(ui, price) {
    try {
        var file = File.saveDialog('حفظ تقدير السعر - CSV', '*.csv');
        if (!file) return false;
        
        var report = generateDetailedReport(ui, price);
        
        file.open('w');
        file.encoding = 'UTF-8';
        
        // رأس CSV
        file.writeln('التاريخ,الوقت,نوع التصميم,الساعات,التعقيد,التعديلات,نوع الاستخدام,سرعة التسليم,السعر الأساسي,معامل التعقيد,معامل الاستخدام,معامل التسليم,تكلفة التعديلات,السعر النهائي');
        
        // بيانات التقدير
        var csvLine = [
            report.timestamp.toLocaleDateString(),
            report.timestamp.toLocaleTimeString(),
            report.projectDetails.designType,
            report.projectDetails.hours,
            report.projectDetails.complexity,
            report.projectDetails.revisions,
            PricingConfig.usageMultipliers[report.usageType].name,
            PricingConfig.deliveryMultipliers[report.deliveryType].name,
            Math.round(report.calculations.basePrice),
            report.calculations.complexityMultiplier,
            report.calculations.usageMultiplier,
            report.calculations.deliveryMultiplier,
            report.calculations.revisionsCost,
            report.finalPrice
        ].join(',');
        
        file.writeln(csvLine);
        file.close();
        return true;
        
    } catch (error) {
        alert('خطأ في حفظ ملف CSV: ' + error.message);
        return false;
    }
}

// دالة حفظ كملف JSON للتطبيقات الأخرى
function saveAsJSON(ui, price) {
    try {
        var file = File.saveDialog('حفظ تقدير السعر - JSON', '*.json');
        if (!file) return false;
        
        var report = generateDetailedReport(ui, price);
        
        // إنشاء كائن JSON
        var jsonData = {
            version: "1.0",
            generator: "Design Price Calculator",
            timestamp: report.timestamp.toISOString(),
            estimate: {
                finalPrice: report.finalPrice,
                currency: "USD",
                validUntil: new Date(report.timestamp.getTime() + 30 * 24 * 60 * 60 * 1000).toISOString(),
                project: {
                    designType: report.projectDetails.designType,
                    designTypeKey: report.projectDetails.designTypeKey,
                    estimatedHours: report.projectDetails.hours,
                    complexityLevel: report.projectDetails.complexity,
                    revisionsCount: report.projectDetails.revisions,
                    usageType: report.usageType,
                    deliveryType: report.deliveryType
                },
                pricing: {
                    basePrice: Math.round(report.calculations.basePrice),
                    multipliers: {
                        complexity: report.calculations.complexityMultiplier,
                        usage: report.calculations.usageMultiplier,
                        delivery: report.calculations.deliveryMultiplier
                    },
                    additionalCosts: {
                        revisions: report.calculations.revisionsCost,
                        freeRevisionsUsed: Math.min(report.projectDetails.revisions, report.calculations.freeRevisions),
                        paidRevisions: report.calculations.paidRevisions
                    }
                },
                breakdown: {
                    subtotal: Math.round(report.calculations.priceBeforeRevisions),
                    revisionsCost: report.calculations.revisionsCost,
                    total: report.finalPrice
                }
            }
        };
        
        file.open('w');
        file.encoding = 'UTF-8';
        file.write(JSON.stringify(jsonData, null, 2));
        file.close();
        return true;
        
    } catch (error) {
        alert('خطأ في حفظ ملف JSON: ' + error.message);
        return false;
    }
}

// دالة إنشاء عرض سعر احترافي
function generateQuoteDocument(ui, price) {
    try {
        var file = File.saveDialog('حفظ عرض السعر', '*.txt');
        if (!file) return false;
        
        var report = generateDetailedReport(ui, price);
        
        file.open('w');
        file.encoding = 'UTF-8';
        
        // رأس عرض السعر
        file.writeln('╔══════════════════════════════════════════════════════════════╗');
        file.writeln('║                           عرض سعر                            ║');
        file.writeln('║                      PRICE QUOTATION                        ║');
        file.writeln('╚══════════════════════════════════════════════════════════════╝');
        file.writeln('');
        
        // معلومات المقدم
        file.writeln('من: [اسم المصمم/الاستوديو]');
        file.writeln('إلى: [اسم العميل]');
        file.writeln('التاريخ: ' + report.timestamp.toLocaleDateString());
        file.writeln('رقم العرض: QT-' + report.timestamp.getFullYear() + 
                     String(report.timestamp.getMonth() + 1).padStart(2, '0') + 
                     String(report.timestamp.getDate()).padStart(2, '0') + '-' + 
                     String(Math.floor(Math.random() * 1000)).padStart(3, '0'));
        file.writeln('');
        
        // وصف المشروع
        file.writeln('┌─ 📋 وصف المشروع ────────────────────────────────────────────┐');
        file.writeln('│ نوع الخدمة: ' + report.projectDetails.designType);
        file.writeln('│ نطاق العمل: تصميم احترافي حسب المتطلبات المحددة');
        file.writeln('│ مدة التنفيذ: ' + Math.ceil(report.projectDetails.hours / 8) + ' أيام عمل');
        file.writeln('│ عدد التعديلات: ' + report.calculations.freeRevisions + ' تعديل مجاني');
        file.writeln('│ الملفات المسلمة: ملفات عالية الجودة بصيغ مختلفة');
        file.writeln('└──────────────────────────────────────────────────────────────┘');
        file.writeln('');
        
        // تفاصيل التكلفة
        file.writeln('┌─ 💰 تفاصيل التكلفة ─────────────────────────────────────────┐');
        file.writeln('│ الخدمة الأساسية: $' + Math.round(report.calculations.priceBeforeRevisions));
        if (report.calculations.revisionsCost > 0) {
            file.writeln('│ تعديلات إضافية: $' + report.calculations.revisionsCost);
        }
        file.writeln('│ ─────────────────────────────────────────────────────────────');
        file.writeln('│ المجموع الفرعي: $' + (Math.round(report.calculations.priceBeforeRevisions) + report.calculations.revisionsCost));
        file.writeln('│ الضريبة: $0 (غير مشمولة)');
        file.writeln('│ ═════════════════════════════════════════════════════════════');
        file.writeln('│ 🎯 المجموع الكلي: $' + report.finalPrice);
        file.writeln('└──────────────────────────────────────────────────────────────┘');
        file.writeln('');
        
        // شروط الدفع
        file.writeln('┌─ 💳 شروط الدفع ─────────────────────────────────────────────┐');
        file.writeln('│ • 50% مقدم عند بدء العمل: $' + Math.round(report.finalPrice * 0.5));
        file.writeln('│ • 50% عند التسليم النهائي: $' + Math.round(report.finalPrice * 0.5));
        file.writeln('│ • طرق الدفع: تحويل بنكي، PayPal، أو حسب الاتفاق');
        file.writeln('│ • العملة: دولار أمريكي (USD)');
        file.writeln('└──────────────────────────────────────────────────────────────┘');
        file.writeln('');
        
        // الشروط والأحكام
        file.writeln('┌─ 📜 الشروط والأحكام ────────────────────────────────────────┐');
        file.writeln('│ • هذا العرض صالح لمدة 30 يوماً من تاريخ الإصدار');
        file.writeln('│ • بدء العمل يتطلب موافقة كتابية ودفع المقدم');
        file.writeln('│ • التعديلات الإضافية تتطلب موافقة على التكلفة');
        file.writeln('│ • حقوق الملكية تنتقل للعميل عند السداد الكامل');
        file.writeln('│ • إلغاء المشروع: استرداد جزئي حسب مرحلة العمل');
        file.writeln('└──────────────────────────────────────────────────────────────┘');
        file.writeln('');
        
        // معلومات التواصل
        file.writeln('┌─ 📞 معلومات التواصل ────────────────────────────────────────┐');
        file.writeln('│ الهاتف: [رقم الهاتف]');
        file.writeln('│ البريد الإلكتروني: [البريد الإلكتروني]');
        file.writeln('│ الموقع الإلكتروني: [الموقع]');
        file.writeln('│ العنوان: [العنوان]');
        file.writeln('└──────────────────────────────────────────────────────────────┘');
        file.writeln('');
        
        // تذييل
        file.writeln('═══════════════════════════════════════════════════════════════');
        file.writeln('شكراً لثقتكم - نتطلع للعمل معكم');
        file.writeln('Thank you for your trust - We look forward to working with you');
        file.writeln('═══════════════════════════════════════════════════════════════');
        
        file.close();
        return true;
        
    } catch (error) {
        alert('خطأ في إنشاء عرض السعر: ' + error.message);
        return false;
    }
}

// دالة عرض خيارات التصدير
function showExportOptions(ui, price) {
    var exportDialog = new Window('dialog', 'خيارات التصدير والحفظ');
    exportDialog.orientation = 'column';
    exportDialog.alignChildren = 'fill';
    exportDialog.spacing = 15;
    exportDialog.margins = 20;
    
    var title = exportDialog.add('statictext', undefined, '📁 اختر صيغة الحفظ المناسبة');
    title.graphics.font = ScriptUI.newFont(title.graphics.font.name, ScriptUI.FontStyle.BOLD, 14);
    
    var optionsGroup = exportDialog.add('panel', undefined, 'صيغ الحفظ المتاحة');
    optionsGroup.orientation = 'column';
    optionsGroup.alignChildren = 'fill';
    optionsGroup.margins = 15;
    
    var textOption = optionsGroup.add('radiobutton', undefined, '📄 ملف نصي منسق (للطباعة والعرض)');
    var csvOption = optionsGroup.add('radiobutton', undefined, '📊 ملف CSV (للتحليل والإحصائيات)');
    var jsonOption = optionsGroup.add('radiobutton', undefined, '🔧 ملف JSON (للتطبيقات الأخرى)');
    var quoteOption = optionsGroup.add('radiobutton', undefined, '💼 عرض سعر احترافي (للعملاء)');
    
    textOption.value = true;
    
    var buttonGroup = exportDialog.add('group');
    buttonGroup.orientation = 'row';
    buttonGroup.alignChildren = 'center';
    
    var exportButton = buttonGroup.add('button', undefined, '💾 تصدير');
    var cancelButton = buttonGroup.add('button', undefined, '❌ إلغاء');
    
    exportButton.onClick = function() {
        var success = false;
        
        if (textOption.value) {
            success = saveAsFormattedText(ui, price);
        } else if (csvOption.value) {
            success = saveAsCSV(ui, price);
        } else if (jsonOption.value) {
            success = saveAsJSON(ui, price);
        } else if (quoteOption.value) {
            success = generateQuoteDocument(ui, price);
        }
        
        if (success) {
            alert('✅ تم التصدير بنجاح!');
            exportDialog.close();
        }
    };
    
    cancelButton.onClick = function() {
        exportDialog.close();
    };
    
    exportDialog.show();
}
