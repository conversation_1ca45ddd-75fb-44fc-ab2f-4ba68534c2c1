/*
مولد PDF الاحترافي - Professional PDF Generator
إنشاء عقود تسعير احترافية بتنسيق منظم وخطوط واضحة
*/

// دالة إنشاء مستند PDF احترافي
function createProfessionalPDF(ui, price) {
    try {
        // إنشاء مستند جديد بحجم A4
        var doc = app.documents.add();
        doc.artboards[0].artboardRect = [0, 0, 595.28, 841.89]; // A4 size in points
        
        // إعدادات الخطوط والألوان
        var titleFont = 24;
        var headerFont = 18;
        var bodyFont = 14;
        var smallFont = 12;
        
        var primaryColor = [0, 47, 108]; // أزرق داكن
        var secondaryColor = [128, 128, 128]; // رمادي
        var accentColor = [255, 140, 0]; // برتقالي
        
        var currentY = 750; // البداية من الأعلى
        var leftMargin = 50;
        var rightMargin = 545;
        var lineHeight = 25;
        
        // دالة إضافة نص
        function addText(text, x, y, fontSize, color, align) {
            var textFrame = doc.textFrames.add();
            textFrame.contents = text;
            textFrame.left = x;
            textFrame.top = y;
            
            var textRange = textFrame.textRange;
            textRange.characterAttributes.size = fontSize || bodyFont;
            textRange.characterAttributes.fillColor = createColor(color || [0, 0, 0]);
            
            if (align === 'center') {
                textRange.paragraphAttributes.justification = Justification.CENTER;
            } else if (align === 'right') {
                textRange.paragraphAttributes.justification = Justification.RIGHT;
            }
            
            return textFrame;
        }
        
        // دالة إنشاء لون
        function createColor(rgbArray) {
            var color = new RGBColor();
            color.red = rgbArray[0];
            color.green = rgbArray[1];
            color.blue = rgbArray[2];
            return color;
        }
        
        // دالة إضافة خط فاصل
        function addLine(y, color, thickness) {
            var line = doc.pathItems.add();
            line.setEntirePath([[leftMargin, y], [rightMargin, y]]);
            line.strokeColor = createColor(color || secondaryColor);
            line.strokeWidth = thickness || 1;
            line.filled = false;
        }
        
        // دالة إضافة مربع ملون
        function addColorBox(x, y, width, height, color) {
            var rect = doc.pathItems.rectangle(y, x, width, height);
            rect.fillColor = createColor(color);
            rect.stroked = false;
        }
        
        // === رأس المستند ===
        // مربع ملون للرأس
        addColorBox(leftMargin, currentY + 20, rightMargin - leftMargin, 60, primaryColor);
        
        // عنوان رئيسي
        addText('عقد تسعير مشروع تصميم', leftMargin + 20, currentY, titleFont, [255, 255, 255]);
        addText('DESIGN PROJECT PRICING CONTRACT', leftMargin + 20, currentY - 25, 16, [255, 255, 255]);
        
        currentY -= 80;
        
        // === معلومات التاريخ والرقم ===
        var now = new Date();
        var contractNumber = 'DC-' + now.getFullYear() + '-' + (now.getMonth() + 1).toString().padStart(2, '0') + '-' + now.getDate().toString().padStart(2, '0') + '-' + Math.floor(Math.random() * 1000);
        
        addText('رقم العقد: ' + contractNumber, leftMargin, currentY, bodyFont, primaryColor);
        addText('Contract No: ' + contractNumber, rightMargin - 150, currentY, bodyFont, primaryColor);
        
        currentY -= lineHeight;
        addText('التاريخ: ' + now.toLocaleDateString('ar-SA'), leftMargin, currentY, bodyFont);
        addText('Date: ' + now.toLocaleDateString('en-US'), rightMargin - 150, currentY, bodyFont);
        
        currentY -= lineHeight * 1.5;
        addLine(currentY, primaryColor, 2);
        currentY -= lineHeight;
        
        // === معلومات المشروع ===
        addText('تفاصيل المشروع - PROJECT DETAILS', leftMargin, currentY, headerFont, primaryColor);
        currentY -= lineHeight * 1.2;
        
        // مربع خلفية للمعلومات
        addColorBox(leftMargin, currentY + 15, rightMargin - leftMargin, 120, [245, 245, 245]);
        
        // نوع التصميم
        var designTypeIndex = ui.designTypeDropdown.selection.index;
        var designTypeKeys = Object.keys(PricingConfig.designTypes);
        var designType = PricingConfig.designTypes[designTypeKeys[designTypeIndex]];
        
        addText('نوع المشروع:', leftMargin + 10, currentY, bodyFont, primaryColor);
        addText(designType.name, leftMargin + 120, currentY, bodyFont);
        addText('Project Type:', rightMargin - 200, currentY, bodyFont, primaryColor);
        
        currentY -= lineHeight;
        addText('عدد الساعات:', leftMargin + 10, currentY, bodyFont, primaryColor);
        addText(ui.timeInput.text + ' ساعة', leftMargin + 120, currentY, bodyFont);
        addText('Hours: ' + ui.timeInput.text, rightMargin - 200, currentY, bodyFont, primaryColor);
        
        currentY -= lineHeight;
        addText('درجة التعقيد:', leftMargin + 10, currentY, bodyFont, primaryColor);
        addText(ui.complexityValue.text, leftMargin + 120, currentY, bodyFont);
        addText('Complexity: ' + ui.complexityValue.text, rightMargin - 200, currentY, bodyFont, primaryColor);
        
        currentY -= lineHeight;
        addText('عدد التعديلات:', leftMargin + 10, currentY, bodyFont, primaryColor);
        addText(ui.revisionsInput.text + ' تعديل', leftMargin + 120, currentY, bodyFont);
        addText('Revisions: ' + ui.revisionsInput.text, rightMargin - 200, currentY, bodyFont, primaryColor);
        
        currentY -= lineHeight * 1.5;
        addLine(currentY, secondaryColor);
        currentY -= lineHeight;
        
        // === تفاصيل التسعير ===
        addText('تفاصيل التسعير - PRICING BREAKDOWN', leftMargin, currentY, headerFont, primaryColor);
        currentY -= lineHeight * 1.2;
        
        // مربع خلفية للتسعير
        addColorBox(leftMargin, currentY + 15, rightMargin - leftMargin, 100, [250, 250, 255]);
        
        // السعر الأساسي
        var basePrice = designType.basePrice * parseFloat(ui.timeInput.text);
        addText('السعر الأساسي:', leftMargin + 10, currentY, bodyFont, primaryColor);
        addText('$' + Math.round(basePrice), leftMargin + 150, currentY, bodyFont);
        addText('Base Price: $' + Math.round(basePrice), rightMargin - 200, currentY, bodyFont, primaryColor);
        
        currentY -= lineHeight;
        
        // المعاملات
        var complexityLevel = Math.round(ui.complexitySlider.value);
        var complexityKeys = Object.keys(PricingConfig.complexityMultipliers);
        var complexityData = PricingConfig.complexityMultipliers[complexityKeys[complexityLevel - 1]];
        
        addText('معامل التعقيد:', leftMargin + 10, currentY, bodyFont, primaryColor);
        addText('×' + complexityData.multiplier, leftMargin + 150, currentY, bodyFont);
        addText('Complexity Factor: ×' + complexityData.multiplier, rightMargin - 200, currentY, bodyFont, primaryColor);
        
        currentY -= lineHeight;
        
        // التعديلات الإضافية
        var revisions = parseInt(ui.revisionsInput.text);
        var freeRevisions = 2;
        var paidRevisions = Math.max(0, revisions - freeRevisions);
        var revisionCost = 15;
        if (typeof PricingConfig !== 'undefined' && PricingConfig.additionalCosts && PricingConfig.additionalCosts.revisionCost) {
            revisionCost = PricingConfig.additionalCosts.revisionCost;
        }
        var revisionsCost = paidRevisions * revisionCost;
        
        if (paidRevisions > 0) {
            addText('تعديلات إضافية:', leftMargin + 10, currentY, bodyFont, primaryColor);
            addText(paidRevisions + ' × $' + revisionCost + ' = $' + revisionsCost, leftMargin + 150, currentY, bodyFont);
            addText('Additional Revisions: $' + revisionsCost, rightMargin - 200, currentY, bodyFont, primaryColor);
        } else {
            addText('التعديلات مشمولة:', leftMargin + 10, currentY, bodyFont, primaryColor);
            addText('مجاناً', leftMargin + 150, currentY, bodyFont, [0, 128, 0]);
            addText('Revisions: Included', rightMargin - 200, currentY, bodyFont, [0, 128, 0]);
        }
        
        currentY -= lineHeight * 1.5;
        addLine(currentY, accentColor, 3);
        currentY -= lineHeight;
        
        // === السعر النهائي ===
        addColorBox(leftMargin, currentY + 15, rightMargin - leftMargin, 40, accentColor);
        
        addText('إجمالي المبلغ المستحق', leftMargin + 20, currentY, headerFont, [255, 255, 255]);
        addText('TOTAL AMOUNT DUE', leftMargin + 20, currentY - 20, 14, [255, 255, 255]);
        
        addText(ui.currencySymbol.text + Math.round(price), rightMargin - 100, currentY - 5, 28, [255, 255, 255], 'right');
        
        currentY -= lineHeight * 2.5;
        
        // === الشروط والأحكام ===
        addText('الشروط والأحكام - TERMS & CONDITIONS', leftMargin, currentY, headerFont, primaryColor);
        currentY -= lineHeight;
        
        var terms = [
            '• هذا العرض صالح لمدة 30 يوماً من تاريخ الإصدار',
            '• يتطلب دفع 50% مقدماً لبدء العمل',
            '• المبلغ المتبقي يُدفع عند التسليم النهائي',
            '• التعديلات الإضافية تتطلب موافقة مسبقة',
            '• مدة التسليم تبدأ من تاريخ استلام المقدم',
            '• حقوق الملكية تنتقل للعميل عند السداد الكامل'
        ];
        
        for (var i = 0; i < terms.length; i++) {
            addText(terms[i], leftMargin, currentY, smallFont);
            currentY -= lineHeight * 0.8;
        }
        
        currentY -= lineHeight;
        addLine(currentY, secondaryColor);
        currentY -= lineHeight;
        
        // === معلومات الاتصال ===
        addText('للاستفسارات والتواصل:', leftMargin, currentY, bodyFont, primaryColor);
        addText('For inquiries and contact:', rightMargin - 200, currentY, bodyFont, primaryColor);
        
        currentY -= lineHeight;
        addText('البريد الإلكتروني: <EMAIL>', leftMargin, currentY, smallFont);
        addText('الهاتف: +966 50 123 4567', leftMargin, currentY - 15, smallFont);
        
        // === تذييل ===
        addText('تم إنشاؤه بواسطة حاسبة أسعار التصميم الاحترافية v2.0', leftMargin, 50, 10, secondaryColor);
        addText('Generated by Design Price Calculator Pro v2.0', rightMargin - 250, 50, 10, secondaryColor);
        
        // حفظ المستند
        var saveDialog = File.saveDialog('حفظ عقد التسعير - Save Pricing Contract', '*.pdf');
        if (saveDialog) {
            try {
                var pdfOptions = new ExportOptionsPDF();
                pdfOptions.pDFPreset = '[High Quality Print]';
                pdfOptions.acrobatLayers = true;
                pdfOptions.preserveEditability = false;

                doc.exportFile(saveDialog, ExportType.PDF, pdfOptions);
            } catch (pdfError) {
                // في حالة فشل تصدير PDF، احفظ كـ AI
                doc.saveAs(saveDialog);
                alert('⚠️ تم حفظ الملف كـ Illustrator بدلاً من PDF\n\nيمكنك تصديره إلى PDF لاحقاً من Illustrator');
            }
            doc.close(SaveOptions.DONOTSAVECHANGES);
            
            alert('✅ تم إنشاء عقد التسعير بنجاح!\n\n' +
                  '📄 الملف محفوظ في:\n' + saveDialog.fsName + '\n\n' +
                  '🎯 العقد يحتوي على:\n' +
                  '• تفاصيل المشروع الكاملة\n' +
                  '• تفاصيل التسعير المفصلة\n' +
                  '• الشروط والأحكام\n' +
                  '• تنسيق احترافي للطباعة');
            
            return true;
        } else {
            doc.close(SaveOptions.DONOTSAVECHANGES);
            return false;
        }
        
    } catch (error) {
        alert('❌ خطأ في إنشاء ملف PDF:\n\n' + error.message + '\n\n' +
              '💡 تأكد من:\n' +
              '• وجود Adobe Illustrator\n' +
              '• صلاحيات الكتابة في المجلد\n' +
              '• عدم فتح ملف بنفس الاسم');
        return false;
    }
}

// دالة PDF محسنة للاستخدام مع الحاسبة
function exportToPDF(ui, price) {
    try {
        // التحقق من البرنامج المستخدم
        var appName = app.name;

        if (appName.indexOf('Illustrator') !== -1) {
            // استخدام Illustrator لإنشاء PDF احترافي
            var result = confirm('🎨 تم اكتشاف Adobe Illustrator!\n\n' +
                               'هل تريد إنشاء عقد تسعير احترافي بصيغة PDF؟\n\n' +
                               '📄 سيتم إنشاء عقد منسق يحتوي على:\n' +
                               '• رأس احترافي بألوان منسقة\n' +
                               '• تفاصيل المشروع كاملة\n' +
                               '• تفاصيل التسعير المفصلة\n' +
                               '• الشروط والأحكام\n' +
                               '• تنسيق جاهز للطباعة');

            if (result) {
                return createProfessionalPDF(ui, price);
            }

        } else if (appName.indexOf('Photoshop') !== -1) {
            // استخدام Photoshop لإنشاء PDF مبسط
            return createPhotoshopPDF(ui, price);

        } else {
            // برنامج غير مدعوم
            return exportToPDFAlternative(ui, price);
        }

        return false;

    } catch (error) {
        alert('❌ خطأ في تصدير PDF: ' + error.message);
        return exportToPDFAlternative(ui, price);
    }
}

// دالة إنشاء PDF باستخدام Photoshop
function createPhotoshopPDF(ui, price) {
    try {
        var result = confirm('📄 سيتم إنشاء عقد تسعير احترافي باستخدام Photoshop\n\n' +
                           '🎨 سيتم إنشاء:\n' +
                           '• مستند عالي الجودة\n' +
                           '• تنسيق احترافي\n' +
                           '• خطوط واضحة وكبيرة\n' +
                           '• تصدير PDF مباشر\n\n' +
                           'هل تريد المتابعة؟');

        if (!result) return false;

        // إنشاء مستند جديد بحجم A4
        var docWidth = 2480; // A4 width at 300 DPI
        var docHeight = 3508; // A4 height at 300 DPI
        var doc = app.documents.add(docWidth, docHeight, 300, 'عقد تسعير - Pricing Contract');

        // إعدادات الألوان والخطوط
        var primaryColor = new SolidColor();
        primaryColor.rgb.red = 0;
        primaryColor.rgb.green = 47;
        primaryColor.rgb.blue = 108;

        var whiteColor = new SolidColor();
        whiteColor.rgb.red = 255;
        whiteColor.rgb.green = 255;
        whiteColor.rgb.blue = 255;

        var blackColor = new SolidColor();
        blackColor.rgb.red = 0;
        blackColor.rgb.green = 0;
        blackColor.rgb.blue = 0;

        // إضافة طبقة خلفية بيضاء
        var bgLayer = doc.artLayers.add();
        bgLayer.name = 'Background';
        doc.selection.selectAll();
        doc.selection.fill(whiteColor);
        doc.selection.deselect();

        // إضافة طبقة النص
        var textLayer = doc.artLayers.add();
        textLayer.kind = LayerKind.TEXT;
        textLayer.name = 'Contract Content';

        // إنشاء محتوى العقد
        var contractContent = createContractContent(ui, price);

        var textItem = textLayer.textItem;
        textItem.contents = contractContent;
        textItem.font = 'Arial';
        textItem.size = 24;
        textItem.color = blackColor;
        textItem.position = [100, 300];

        // حفظ كـ PDF
        var saveDialog = File.saveDialog('حفظ عقد التسعير - Save Pricing Contract', '*.pdf');
        if (saveDialog) {
            var pdfOptions = new PDFSaveOptions();
            pdfOptions.embedColorProfile = true;
            pdfOptions.embedFonts = true;
            pdfOptions.useOutlines = false;
            pdfOptions.downSample = DownSampleType.NONE;
            pdfOptions.jpegQuality = 12;

            doc.saveAs(saveDialog, pdfOptions, true);
            doc.close(SaveOptions.DONOTSAVECHANGES);

            alert('✅ تم إنشاء عقد التسعير بنجاح!\n\n' +
                  '📄 الملف محفوظ في:\n' + saveDialog.fsName + '\n\n' +
                  '🎯 العقد يحتوي على:\n' +
                  '• تفاصيل المشروع الكاملة\n' +
                  '• تفاصيل التسعير المفصلة\n' +
                  '• الشروط والأحكام\n' +
                  '• تنسيق احترافي للطباعة');

            return true;
        } else {
            doc.close(SaveOptions.DONOTSAVECHANGES);
            return false;
        }

    } catch (error) {
        alert('❌ خطأ في إنشاء PDF باستخدام Photoshop:\n\n' + error.message);
        return exportToPDFAlternative(ui, price);
    }
}

// دالة إنشاء محتوى العقد
function createContractContent(ui, price) {
    var content = '';
    var now = new Date();
    var contractNumber = 'DC-' + now.getFullYear() + '-' + (now.getMonth() + 1).toString() + '-' + now.getDate() + '-' + Math.floor(Math.random() * 1000);

    // رأس العقد
    content += '═══════════════════════════════════════════════════════════\n';
    content += '                    عقد تسعير مشروع تصميم                    \n';
    content += '                DESIGN PROJECT PRICING CONTRACT                \n';
    content += '═══════════════════════════════════════════════════════════\n\n';

    // معلومات العقد
    content += 'رقم العقد / Contract No: ' + contractNumber + '\n';
    content += 'التاريخ / Date: ' + now.toLocaleDateString() + '\n\n';

    // تفاصيل المشروع
    content += '📋 تفاصيل المشروع - PROJECT DETAILS\n';
    content += '───────────────────────────────────────────────────────────\n';

    var designTypeIndex = ui.designTypeDropdown.selection.index;
    var designTypeKeys = Object.keys(PricingConfig.designTypes);
    var designType = PricingConfig.designTypes[designTypeKeys[designTypeIndex]];

    content += '🎨 نوع المشروع / Project Type: ' + designType.name + '\n';
    content += '⏰ عدد الساعات / Hours: ' + ui.timeInput.text + ' ساعة\n';
    content += '🔧 درجة التعقيد / Complexity: ' + ui.complexityValue.text + '\n';
    content += '✏️ عدد التعديلات / Revisions: ' + ui.revisionsInput.text + ' تعديل\n\n';

    // تفاصيل التسعير
    content += '💰 تفاصيل التسعير - PRICING BREAKDOWN\n';
    content += '───────────────────────────────────────────────────────────\n';

    var basePrice = designType.basePrice * parseFloat(ui.timeInput.text);
    content += '💵 السعر الأساسي / Base Price: $' + Math.round(basePrice) + '\n';

    var complexityLevel = Math.round(ui.complexitySlider.value);
    var complexityKeys = Object.keys(PricingConfig.complexityMultipliers);
    var complexityData = PricingConfig.complexityMultipliers[complexityKeys[complexityLevel - 1]];
    content += '🔧 معامل التعقيد / Complexity Factor: ×' + complexityData.multiplier + '\n';

    // التعديلات
    var revisions = parseInt(ui.revisionsInput.text);
    var freeRevisions = 2;
    var paidRevisions = Math.max(0, revisions - freeRevisions);
    var revisionCost = 15;
    if (typeof PricingConfig !== 'undefined' && PricingConfig.additionalCosts && PricingConfig.additionalCosts.revisionCost) {
        revisionCost = PricingConfig.additionalCosts.revisionCost;
    }

    if (paidRevisions > 0) {
        var revisionsCost = paidRevisions * revisionCost;
        content += '✏️ تعديلات إضافية / Additional Revisions: ' + paidRevisions + ' × $' + revisionCost + ' = $' + revisionsCost + '\n';
    } else {
        content += '✏️ التعديلات مشمولة / Revisions Included: مجاناً\n';
    }

    content += '\n';
    content += '═══════════════════════════════════════════════════════════\n';
    content += '🎯 إجمالي المبلغ المستحق / TOTAL AMOUNT DUE\n';
    content += ui.currencySymbol.text + Math.round(price) + '\n';
    content += '═══════════════════════════════════════════════════════════\n\n';

    // الشروط والأحكام
    content += '📋 الشروط والأحكام - TERMS & CONDITIONS\n';
    content += '───────────────────────────────────────────────────────────\n';
    content += '• هذا العرض صالح لمدة 30 يوماً من تاريخ الإصدار\n';
    content += '• يتطلب دفع 50% مقدماً لبدء العمل\n';
    content += '• المبلغ المتبقي يُدفع عند التسليم النهائي\n';
    content += '• التعديلات الإضافية تتطلب موافقة مسبقة\n';
    content += '• مدة التسليم تبدأ من تاريخ استلام المقدم\n';
    content += '• حقوق الملكية تنتقل للعميل عند السداد الكامل\n\n';

    // معلومات الاتصال
    content += '📞 للاستفسارات والتواصل - For inquiries and contact:\n';
    content += 'البريد الإلكتروني: <EMAIL>\n';
    content += 'الهاتف: +966 50 123 4567\n\n';

    // تذييل
    content += '───────────────────────────────────────────────────────────\n';
    content += 'تم إنشاؤه بواسطة حاسبة أسعار التصميم الاحترافية v2.0\n';
    content += 'Generated by Design Price Calculator Pro v2.0\n';
    content += '───────────────────────────────────────────────────────────';

    return content;
}

// دالة بديلة لإنشاء PDF (ملف نصي منسق)
function exportToPDFAlternative(ui, price) {
    alert('📄 سيتم إنشاء ملف نصي منسق بدلاً من PDF\n\n' +
          'يمكنك تحويله إلى PDF لاحقاً باستخدام:\n' +
          '• Microsoft Word\n' +
          '• Google Docs\n' +
          '• أي محرر نصوص يدعم التصدير إلى PDF');
    
    if (typeof saveEstimateWithLanguage === 'function') {
        return saveEstimateWithLanguage(ui, price, 'arabic');
    } else {
        alert('❌ وظائف التصدير غير متاحة');
        return false;
    }
}
